<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="FMEA信号接口智能映射工具 - 基于AI的汽车电子信号自动映射系统" />
    <meta name="keywords" content="FMEA,信号映射,汽车电子,AI,智能映射,接口定义" />
    <meta name="author" content="FMEA Mapper Team" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="FMEA信号接口智能映射工具" />
    <meta property="og:description" content="基于AI的汽车电子信号自动映射系统" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="FMEA信号接口智能映射工具" />
    <meta property="twitter:description" content="基于AI的汽车电子信号自动映射系统" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- Theme color for mobile browsers -->
    <meta name="theme-color" content="#667eea" />
    <meta name="msapplication-TileColor" content="#667eea" />

    <!-- Preconnect for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Title -->
    <title>FMEA信号接口智能映射工具</title>

    <!-- Loading styles -->
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .loading-logo {
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 2rem;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      .loading-text {
        font-size: 1.1rem;
        opacity: 0.9;
        text-align: center;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      /* Hide app initially */
      #app {
        opacity: 0;
        transition: opacity 0.5s ease-in-out;
      }

      #app.loaded {
        opacity: 1;
      }

      /* Ensure body has no margin/padding */
      body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">
        🔧 FMEA信号接口智能映射工具
      </div>
      <div class="loading-spinner"></div>
      <div class="loading-text">
        正在加载应用程序...
      </div>
    </div>

    <!-- Main App -->
    <div id="app"></div>

    <!-- Scripts -->
    <script type="module" src="/src/main.js"></script>

    <!-- Loading script -->
    <script>
      // Hide loading screen when app is ready
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          const app = document.getElementById('app');

          if (loadingScreen && app) {
            loadingScreen.style.opacity = '0';
            app.classList.add('loaded');

            setTimeout(function() {
              loadingScreen.style.display = 'none';
            }, 500);
          }
        }, 1000); // Show loading for at least 1 second
      });

      // Error handling
      window.addEventListener('error', function(e) {
        console.error('Application error:', e.error);
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
          loadingText.textContent = '应用程序加载失败，请刷新页面重试';
          loadingText.style.color = '#ff6b6b';
        }
      });

      // Service worker registration (for future PWA support)
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          // Uncomment when service worker is implemented
          // navigator.serviceWorker.register('/sw.js');
        });
      }
    </script>
  </body>
</html>
