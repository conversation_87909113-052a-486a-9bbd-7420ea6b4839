#!/usr/bin/env node

/**
 * FMEA信号接口智能映射工具 - 前端配置检查脚本
 * 用于验证前端环境变量配置是否正确
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 颜色输出函数
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  reset: '\x1b[0m'
}

const log = {
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.cyan}🔍 ${msg}${colors.reset}`)
}

// 检查.env文件是否存在
function checkEnvFile() {
  const envFile = path.join(__dirname, '.env')
  const envExampleFile = path.join(__dirname, '.env.example')
  
  if (!fs.existsSync(envFile)) {
    log.error('.env文件不存在')
    if (fs.existsSync(envExampleFile)) {
      log.info('请复制 .env.example 为 .env 并配置相应的值')
      log.info('命令: cp .env.example .env')
    }
    return false
  }
  
  log.success('.env文件存在')
  return true
}

// 解析.env文件
function parseEnvFile() {
  const envFile = path.join(__dirname, '.env')
  
  if (!fs.existsSync(envFile)) {
    return {}
  }
  
  const envContent = fs.readFileSync(envFile, 'utf8')
  const envVars = {}
  
  envContent.split('\n').forEach(line => {
    line = line.trim()
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=')
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim()
      }
    }
  })
  
  return envVars
}

// 检查必需的环境变量
function checkRequiredVars(envVars) {
  const requiredVars = {
    'VITE_APP_TITLE': '应用标题',
    'VITE_APP_VERSION': '应用版本',
    'VITE_API_BASE_URL': 'API基础URL'
  }
  
  const missingVars = []
  const invalidVars = []
  
  for (const [varName, description] of Object.entries(requiredVars)) {
    const value = envVars[varName]
    
    if (!value) {
      missingVars.push(`${varName} (${description})`)
    } else {
      // 特殊验证
      if (varName === 'VITE_API_BASE_URL' && !value.startsWith('http')) {
        invalidVars.push(`${varName} - 必须是有效的HTTP URL`)
      }
    }
  }
  
  if (missingVars.length > 0) {
    log.error('缺少必需的环境变量:')
    missingVars.forEach(varName => console.log(`   - ${varName}`))
  }
  
  if (invalidVars.length > 0) {
    log.warning('环境变量配置有问题:')
    invalidVars.forEach(varName => console.log(`   - ${varName}`))
  }
  
  if (missingVars.length === 0 && invalidVars.length === 0) {
    log.success('所有必需的环境变量都已正确配置')
    return true
  }
  
  return false
}

// 检查可选的环境变量
function checkOptionalVars(envVars) {
  const optionalVars = {
    'VITE_API_TIMEOUT': ['120000', 'API请求超时时间(毫秒)'],
    'VITE_DEBUG_MODE': ['true', '是否启用调试模式'],
    'VITE_MAX_FILE_SIZE': ['10', '最大文件大小(MB)'],
    'VITE_ALLOWED_FILE_TYPES': ['.txt,.csv', '支持的文件类型'],
    'VITE_THEME_COLOR': ['#667eea', '主题色'],
    'VITE_DEFAULT_LOCALE': ['zh-CN', '默认语言']
  }
  
  console.log('\n📋 可选环境变量配置:')
  for (const [varName, [defaultValue, description]] of Object.entries(optionalVars)) {
    const value = envVars[varName] || defaultValue
    console.log(`   ${varName}: ${value} (${description})`)
  }
}

// 检查package.json
function checkPackageJson() {
  const packageFile = path.join(__dirname, 'package.json')
  
  if (!fs.existsSync(packageFile)) {
    log.error('package.json文件不存在')
    return false
  }
  
  try {
    const packageContent = JSON.parse(fs.readFileSync(packageFile, 'utf8'))
    
    // 检查必需的依赖
    const requiredDeps = ['vue', 'element-plus', 'axios']
    const missingDeps = []
    
    for (const dep of requiredDeps) {
      if (!packageContent.dependencies || !packageContent.dependencies[dep]) {
        missingDeps.push(dep)
      }
    }
    
    if (missingDeps.length > 0) {
      log.error('缺少必需的依赖包:')
      missingDeps.forEach(dep => console.log(`   - ${dep}`))
      log.info('请运行: npm install')
      return false
    }
    
    log.success('package.json配置正确')
    return true
    
  } catch (error) {
    log.error(`package.json解析失败: ${error.message}`)
    return false
  }
}

// 检查node_modules
function checkNodeModules() {
  const nodeModulesDir = path.join(__dirname, 'node_modules')
  
  if (!fs.existsSync(nodeModulesDir)) {
    log.error('node_modules目录不存在')
    log.info('请运行: npm install')
    return false
  }
  
  log.success('依赖包已安装')
  return true
}

// 主函数
function main() {
  console.log('🔍 FMEA信号接口智能映射工具 - 前端配置检查')
  console.log('='.repeat(60))
  
  const checks = [
    ['检查.env文件', checkEnvFile],
    ['检查package.json', checkPackageJson],
    ['检查依赖包', checkNodeModules]
  ]
  
  let allPassed = true
  let envVars = {}
  
  for (const [name, checkFunc] of checks) {
    log.title(`${name}...`)
    if (!checkFunc()) {
      allPassed = false
    }
  }
  
  // 如果.env文件存在，检查环境变量
  if (fs.existsSync(path.join(__dirname, '.env'))) {
    envVars = parseEnvFile()
    
    log.title('检查环境变量...')
    if (!checkRequiredVars(envVars)) {
      allPassed = false
    }
    
    checkOptionalVars(envVars)
  }
  
  // 总结
  console.log('\n' + '='.repeat(60))
  if (allPassed) {
    log.success('前端配置检查通过！可以启动开发服务器了')
    log.info('运行命令: npm run dev')
  } else {
    log.error('前端配置检查失败，请修复上述问题后重试')
    log.info('参考文档: README.md')
  }
  
  return allPassed ? 0 : 1
}

// 运行检查
process.exit(main())
