# FMEA信号接口智能映射工具 - 前端环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# ===========================================
# 应用配置
# ===========================================

# 应用标题
VITE_APP_TITLE=FMEA信号接口智能映射工具

# 应用版本
VITE_APP_VERSION=1.0.0

# 应用描述
VITE_APP_DESCRIPTION=基于AI的汽车电子信号自动映射系统

# ===========================================
# API配置
# ===========================================

# 后端API基础URL (开发环境)
VITE_API_BASE_URL=http://localhost:8000

# API请求超时时间 (毫秒)
VITE_API_TIMEOUT=120000

# ===========================================
# 功能开关
# ===========================================

# 是否启用调试模式
VITE_DEBUG_MODE=true

# 是否启用性能监控
VITE_ENABLE_PERFORMANCE=true

# 是否启用错误报告
VITE_ENABLE_ERROR_REPORTING=false

# ===========================================
# 文件上传配置
# ===========================================

# 最大文件大小 (MB)
VITE_MAX_FILE_SIZE=10

# 支持的文件类型
VITE_ALLOWED_FILE_TYPES=.txt,.csv,.xlsx,.xls

# ===========================================
# UI配置
# ===========================================

# 主题色
VITE_THEME_COLOR=#667eea

# 是否启用暗色模式
VITE_ENABLE_DARK_MODE=false

# 默认语言
VITE_DEFAULT_LOCALE=zh-CN

# ===========================================
# 第三方服务配置
# ===========================================

# Google Analytics ID (可选)
VITE_GA_ID=

# Sentry DSN (可选，用于错误监控)
VITE_SENTRY_DSN=

# ===========================================
# 使用说明
# ===========================================
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 根据您的环境修改相应的配置值
# 3. 以 VITE_ 开头的变量会被Vite注入到客户端代码中
# 4. 重启开发服务器以使配置生效
