<template>
  <el-container class="app-container">
    <el-header class="app-header">
      <h1>{{ appTitle }}</h1>
    </el-header>
    
    <el-main class="app-main">
      <el-card class="upload-card">
        <template #header>
          <div class="card-header">
            <span>文件上传</span>
          </div>
        </template>
        
        <div class="upload-section">
          <!-- 平台接口文件上传 -->
          <div class="upload-item">
            <el-text class="upload-label" size="large" tag="b">
              平台接口文件：
            </el-text>
            <el-upload
              ref="platformUploadRef"
              class="upload-demo"
              :auto-upload="false"
              :on-change="handlePlatformFileChange"
              :file-list="platformFileList"
              :limit="1"
              accept=".txt,.csv,.xlsx,.xls"
            >
              <template #trigger>
                <el-button type="primary" :icon="Upload">
                  选择平台接口文件
                </el-button>
              </template>
              <template #tip>
                <div class="el-upload__tip">
                  支持 .txt, .csv, .xlsx, .xls 格式文件
                </div>
              </template>
            </el-upload>
          </div>

          <!-- 客户接口文件上传 -->
          <div class="upload-item">
            <el-text class="upload-label" size="large" tag="b">
              客户接口文件：
            </el-text>
            <el-upload
              ref="customerUploadRef"
              class="upload-demo"
              :auto-upload="false"
              :on-change="handleCustomerFileChange"
              :file-list="customerFileList"
              :limit="1"
              accept=".txt,.csv,.xlsx,.xls"
            >
              <template #trigger>
                <el-button type="primary" :icon="Upload">
                  选择客户接口文件
                </el-button>
              </template>
              <template #tip>
                <div class="el-upload__tip">
                  支持 .txt, .csv, .xlsx, .xls 格式文件
                </div>
              </template>
            </el-upload>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <el-button 
            type="success" 
            size="large" 
            :loading="isLoading"
            :disabled="!platformFile || !customerFile"
            @click="startMapping"
            :icon="Cpu"
          >
            {{ isLoading ? '智能映射中...' : '开始智能映射' }}
          </el-button>
        </div>

        <!-- 进度提示 -->
        <div v-if="isLoading" class="progress-section">
          <el-progress 
            :percentage="progressPercentage" 
            :status="progressStatus"
            :stroke-width="8"
          />
          <el-text class="progress-text">{{ progressText }}</el-text>
        </div>

        <!-- 结果展示 -->
        <div v-if="mappingResult" class="result-section">
          <el-alert
            title="映射完成！"
            type="success"
            :description="`成功映射了 ${mappingResult.length} 个信号，点击下载按钮获取详细结果。`"
            show-icon
            :closable="false"
          />
          <el-button 
            type="primary" 
            size="large" 
            @click="downloadResult"
            :icon="Download"
            class="download-btn"
          >
            下载映射结果 (Excel)
          </el-button>
        </div>
      </el-card>
    </el-main>
  </el-container>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Cpu, Download } from '@element-plus/icons-vue'
import axios from 'axios'

// 应用配置
const appTitle = import.meta.env.VITE_APP_TITLE || 'FMEA信号接口智能映射工具'

// 响应式数据
const platformFile = ref(null)
const customerFile = ref(null)
const platformFileList = ref([])
const customerFileList = ref([])
const isLoading = ref(false)
const mappingResult = ref(null)
const progressPercentage = ref(0)
const progressStatus = ref('')
const progressText = ref('')

// API基础URL - 从环境变量获取
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000'
const API_TIMEOUT = parseInt(import.meta.env.VITE_API_TIMEOUT) || 120000
const MAX_FILE_SIZE = parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || 10

// 文件验证函数
const validateFile = (file) => {
  // 检查文件大小
  const maxSizeBytes = MAX_FILE_SIZE * 1024 * 1024
  if (file.size > maxSizeBytes) {
    ElMessage.error(`文件大小不能超过 ${MAX_FILE_SIZE}MB`)
    return false
  }

  // 检查文件类型
  const allowedTypes = import.meta.env.VITE_ALLOWED_FILE_TYPES?.split(',') || ['.txt', '.csv', '.xlsx', '.xls']
  const fileExt = '.' + file.name.split('.').pop().toLowerCase()
  if (!allowedTypes.includes(fileExt)) {
    ElMessage.error(`不支持的文件格式，仅支持: ${allowedTypes.join(', ')}`)
    return false
  }

  return true
}

// 文件上传处理函数
const handlePlatformFileChange = (file) => {
  if (validateFile(file.raw)) {
    platformFile.value = file.raw
    platformFileList.value = [file]
    ElMessage.success('平台接口文件已选择')
  } else {
    platformFileList.value = []
    platformFile.value = null
  }
}

const handleCustomerFileChange = (file) => {
  if (validateFile(file.raw)) {
    customerFile.value = file.raw
    customerFileList.value = [file]
    ElMessage.success('客户接口文件已选择')
  } else {
    customerFileList.value = []
    customerFile.value = null
  }
}

// 更新进度
const updateProgress = (percentage, text, status = '') => {
  progressPercentage.value = percentage
  progressText.value = text
  progressStatus.value = status
}

// 开始映射
const startMapping = async () => {
  if (!platformFile.value || !customerFile.value) {
    ElMessage.error('请先选择两个文件')
    return
  }

  try {
    isLoading.value = true
    mappingResult.value = null
    
    // 模拟进度更新
    updateProgress(10, '正在上传文件...')
    
    // 创建FormData
    const formData = new FormData()
    formData.append('platform_file', platformFile.value)
    formData.append('customer_file', customerFile.value)
    
    updateProgress(30, '文件上传完成，正在调用AI进行智能映射...')
    
    // 发送请求
    const response = await axios.post(
      `${API_BASE_URL}/api/map_signals`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        responseType: 'blob', // 重要：设置响应类型为blob
        timeout: API_TIMEOUT, // 从环境变量获取超时时间
        onUploadProgress: (progressEvent) => {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          updateProgress(30 + percentage * 0.3, '正在上传文件...')
        }
      }
    )
    
    updateProgress(90, '映射完成，正在准备下载...')
    
    // 创建下载链接
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    // 模拟映射结果（用于显示成功信息）
    mappingResult.value = [{ success: true }] // 简化的结果标识
    
    updateProgress(100, '映射完成！', 'success')
    
    // 自动下载文件
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'fmea_mapping_result.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('映射完成！文件已自动下载')
    
  } catch (error) {
    console.error('映射失败:', error)
    updateProgress(0, '', 'exception')
    
    let errorMessage = '映射失败，请重试'
    if (error.response) {
      if (error.response.status === 400) {
        errorMessage = '文件格式不正确或内容为空'
      } else if (error.response.status === 500) {
        errorMessage = '服务器内部错误，请稍后重试'
      }
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请检查网络连接或稍后重试'
    } else if (error.message.includes('Network Error')) {
      errorMessage = '网络连接失败，请检查后端服务是否启动'
    }
    
    ElMessage.error(errorMessage)
  } finally {
    isLoading.value = false
  }
}

// 下载结果（备用下载按钮）
const downloadResult = () => {
  ElMessage.info('文件已在映射完成时自动下载，请检查浏览器下载文件夹')
}
</script>

<style scoped>
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-header h1 {
  color: white;
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-main {
  padding: 40px 20px;
  display: flex;
  justify-content: center;
}

.upload-card {
  width: 100%;
  max-width: 800px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.card-header {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.upload-section {
  margin-bottom: 30px;
}

.upload-item {
  margin-bottom: 30px;
  padding: 20px;
  border: 2px dashed #dcdfe6;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.upload-item:hover {
  border-color: #409eff;
  background-color: #f8f9ff;
}

.upload-label {
  display: block;
  margin-bottom: 15px;
  color: #606266;
}

.upload-demo {
  width: 100%;
}

.action-section {
  text-align: center;
  margin: 30px 0;
}

.progress-section {
  margin: 30px 0;
  text-align: center;
}

.progress-text {
  display: block;
  margin-top: 10px;
  color: #606266;
  font-size: 14px;
}

.result-section {
  margin-top: 30px;
  text-align: center;
}

.download-btn {
  margin-top: 20px;
}

:deep(.el-upload-dragger) {
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  width: 100%;
  height: auto;
  padding: 20px;
}

:deep(.el-upload__tip) {
  margin-top: 10px;
  color: #909399;
  font-size: 12px;
}
</style>
