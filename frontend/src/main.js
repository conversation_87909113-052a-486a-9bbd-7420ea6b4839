import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import App from './App.vue'
import ErrorBoundary from './components/ErrorBoundary.vue'

// 创建Vue应用实例
const app = createApp(App)

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('Vue Error:', err)
  console.error('Component:', instance)
  console.error('Error Info:', info)

  // 在生产环境中，可以发送错误到监控服务
  if (import.meta.env.PROD) {
    // 发送错误报告到服务器
    // sendErrorReport(err, info)
  }
}

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Vue Warning:', msg)
  console.warn('Trace:', trace)
}

// 使用Element Plus
app.use(ElementPlus, {
  // Element Plus 配置
  size: 'default',
  zIndex: 3000,
})

// 注册所有Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册全局组件
app.component('ErrorBoundary', ErrorBoundary)

// 全局属性
app.config.globalProperties.$appName = 'FMEA信号接口智能映射工具'
app.config.globalProperties.$version = '1.0.0'

// 性能监控
if (import.meta.env.DEV) {
  app.config.performance = true
}

// 挂载应用
app.mount('#app')

// 全局未捕获的Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason)
  event.preventDefault()
})

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('Global Error:', event.error)
})

// 应用信息
console.log(`🚀 ${app.config.globalProperties.$appName} v${app.config.globalProperties.$version}`)
console.log('🔧 Environment:', import.meta.env.MODE)
console.log('📦 Vue Version:', app.version)
