<template>
  <div class="error-boundary">
    <el-result
      icon="error"
      title="应用程序出现错误"
      :sub-title="errorMessage"
    >
      <template #extra>
        <el-button type="primary" @click="reload">
          刷新页面
        </el-button>
        <el-button @click="goHome">
          返回首页
        </el-button>
      </template>
    </el-result>
    
    <el-collapse v-if="showDetails" class="error-details">
      <el-collapse-item title="错误详情" name="details">
        <pre class="error-stack">{{ errorStack }}</pre>
      </el-collapse-item>
    </el-collapse>
    
    <div class="error-actions">
      <el-button 
        type="info" 
        size="small" 
        @click="showDetails = !showDetails"
      >
        {{ showDetails ? '隐藏' : '显示' }}错误详情
      </el-button>
      <el-button 
        type="warning" 
        size="small" 
        @click="reportError"
      >
        报告问题
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

// Props
const props = defineProps({
  error: {
    type: Error,
    default: null
  },
  errorInfo: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
const showDetails = ref(false)
const errorMessage = ref('发生了未知错误，请尝试刷新页面')
const errorStack = ref('')

// 初始化错误信息
onMounted(() => {
  if (props.error) {
    errorMessage.value = props.error.message || '发生了未知错误'
    errorStack.value = props.error.stack || '无详细错误信息'
  }
})

// 刷新页面
const reload = () => {
  window.location.reload()
}

// 返回首页
const goHome = () => {
  window.location.href = '/'
}

// 报告错误
const reportError = () => {
  const errorReport = {
    message: errorMessage.value,
    stack: errorStack.value,
    userAgent: navigator.userAgent,
    timestamp: new Date().toISOString(),
    url: window.location.href
  }
  
  // 这里可以发送错误报告到服务器
  console.error('Error Report:', errorReport)
  
  // 复制错误信息到剪贴板
  if (navigator.clipboard) {
    navigator.clipboard.writeText(JSON.stringify(errorReport, null, 2))
      .then(() => {
        ElMessage.success('错误信息已复制到剪贴板')
      })
      .catch(() => {
        ElMessage.warning('无法复制错误信息')
      })
  } else {
    ElMessage.warning('浏览器不支持剪贴板操作')
  }
}
</script>

<style scoped>
.error-boundary {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.error-details {
  width: 100%;
  max-width: 800px;
  margin-top: 20px;
}

.error-stack {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #495057;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 300px;
  overflow-y: auto;
}

.error-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

:deep(.el-result__title) {
  color: #e74c3c;
}

:deep(.el-result__subtitle) {
  color: #7f8c8d;
}
</style>
