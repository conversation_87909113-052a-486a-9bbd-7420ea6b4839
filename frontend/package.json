{"name": "fmea-mapper-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "check-config": "node check-config.js", "start": "npm run check-config && npm run dev"}, "dependencies": {"vue": "^3.3.8", "element-plus": "^2.4.2", "axios": "^1.6.0", "@element-plus/icons-vue": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.0", "vite": "^5.0.0"}}