@echo off
echo ========================================
echo FMEA信号接口智能映射工具 - 安装脚本
echo ========================================

echo.
echo 正在安装后端依赖...
cd backend
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 后端依赖安装失败！
    pause
    exit /b 1
)

echo.
echo 正在安装前端依赖...
cd ..\frontend
npm install
if %errorlevel% neq 0 (
    echo 前端依赖安装失败！
    pause
    exit /b 1
)

cd ..
echo.
echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用说明：
echo 1. 双击 start_backend.bat 启动后端服务
echo 2. 双击 start_frontend.bat 启动前端服务
echo 3. 在浏览器中访问 http://localhost:3000
echo.
echo 示例数据文件位于 sample_data 目录中
echo ========================================
pause
