# Excel文件格式说明

## 支持的Excel文件格式
- `.xlsx` (Excel 2007及以上版本)
- `.xls` (Excel 97-2003版本)

## 文件内容要求

### 数据格式
- **第一列**：信号名称（系统会自动读取第一列的所有数据）
- **其他列**：可以包含其他信息，但系统只会读取第一列

### 示例Excel文件结构

| 信号名称 (A列) | 描述 (B列，可选) | 备注 (C列，可选) |
|----------------|------------------|------------------|
| COM_E_MMot_[©]: input signal ActualEngTorq | 发动机扭矩信号 | 来自动力总成ECU |
| COM_E_PW_[©]: input signal PW "gas pedal-travel" | 油门踏板行程 | 来自动力总成ECU |
| COM_E_KM_[©]: Provide mileage reading | 里程读数 | 来自组合仪表ECU |

### 注意事项

1. **第一列必须包含信号名称**
2. **空行会被自动忽略**
3. **重复的信号名称会被自动去重**
4. **建议在第一行添加标题（如"信号名称"），系统会自动处理**
5. **文件大小不超过10MB**

### 创建Excel文件的步骤

1. 打开Microsoft Excel或WPS表格
2. 在A列输入信号名称，每行一个信号
3. 可选：在B列、C列等添加描述信息
4. 保存为 `.xlsx` 或 `.xls` 格式
5. 上传到FMEA映射工具

### 示例数据

您可以参考 `platform_signals.txt` 和 `customer_signals.txt` 中的内容，将其复制到Excel文件的第一列中。

### 常见问题

**Q: 如果Excel文件有多个工作表，系统会读取哪个？**
A: 系统会读取第一个工作表（默认工作表）的第一列数据。

**Q: 如果第一列有标题行怎么办？**
A: 系统会将所有非空内容都作为信号名称处理，建议标题行使用有意义的名称或在处理前手动删除。

**Q: 支持哪些Excel版本？**
A: 支持Excel 97-2003 (.xls) 和Excel 2007及以上版本 (.xlsx)。
