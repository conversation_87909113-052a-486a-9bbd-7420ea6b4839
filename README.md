# FMEA信号接口智能映射工具

一个基于FastAPI和Vue 3的前后端分离Web应用，用于自动完成汽车电子信号接口的智能映射。

## 项目结构

```
fmea-mapper/
├── backend/          # FastAPI后端服务
│   ├── main.py       # 主应用文件
│   ├── requirements.txt  # Python依赖
│   └── .env.example  # 环境变量示例
└── frontend/         # Vue 3前端应用
    ├── src/          # 源代码
    │   ├── App.vue   # 主组件
    │   └── main.js   # 入口文件
    ├── package.json  # Node.js依赖
    ├── vite.config.js # Vite配置
    └── index.html    # HTML模板
```

## 功能特性

- 📁 **文件上传**: 支持上传平台侧和客户侧接口定义文件
- 🤖 **智能映射**: 调用大语言模型API自动完成信号映射
- 📊 **结果导出**: 生成Excel格式的映射结果文件
- 🎨 **现代界面**: 基于Element Plus的美观用户界面
- ⚡ **实时反馈**: 显示处理进度和状态信息

## 快速开始

### 环境要求

- Python 3.11+
- Node.js 16+
- npm 或 yarn

### 后端启动

1. 进入后端目录：
```bash
cd backend
```

2. 安装Python依赖：
```bash
pip install -r requirements.txt
```

3. 配置环境变量：
```bash
# 复制环境变量示例文件
cp .env.example .env

# 编辑.env文件，配置你的API密钥和其他设置
# 主要需要修改的配置：
# API_KEY=your_actual_api_key_here  # 替换为实际的API密钥
# MODEL=deepseek-chat               # 使用的LLM模型
# BASE_URL=https://api.deepseek.com/v1  # API服务地址
```

3.1. 验证配置：
```bash
# 运行配置检查脚本
python check_config.py
```

4. 启动后端服务：
```bash
python main.py
```

后端服务将在 http://localhost:8000 启动

### 前端启动

1. 进入前端目录：
```bash
cd frontend
```

2. 安装Node.js依赖：
```bash
npm install
```

3. 启动开发服务器：
```bash
npm run dev
```

前端应用将在 http://localhost:3000 启动

## 使用说明

1. **上传文件**: 分别选择平台接口文件和客户接口文件（支持.txt和.csv格式）
2. **开始映射**: 点击"开始智能映射"按钮，系统将调用AI进行信号映射
3. **下载结果**: 映射完成后，系统会自动下载Excel格式的结果文件

## 环境变量配置

### 必需配置
| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `MODEL` | LLM模型名称 | `deepseek-chat` |
| `API_KEY` | API密钥 | `sk-xxx...` |
| `BASE_URL` | API基础URL | `https://api.deepseek.com/v1` |
| `HOST` | 服务器主机地址 | `0.0.0.0` |
| `PORT` | 服务器端口 | `8000` |

### 可选配置
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `REQUEST_TIMEOUT` | API请求超时时间(秒) | `60.0` |
| `MAX_RETRIES` | API请求最大重试次数 | `3` |
| `ALLOWED_ORIGINS` | 允许的前端域名 | `*` |
| `LOG_LEVEL` | 日志级别 | `INFO` |
| `MAX_FILE_SIZE` | 最大文件大小(MB) | `10` |
| `ALLOWED_EXTENSIONS` | 支持的文件扩展名 | `.txt,.csv` |

## 文件格式要求

- 支持的文件格式：`.txt`, `.csv`, `.xlsx`, `.xls`
- 文件编码：UTF-8, GBK, Latin-1（文本文件）
- 内容格式：
  - 文本文件（.txt, .csv）：每行一个信号名称
  - Excel文件（.xlsx, .xls）：第一列为信号名称，自动读取第一列数据
- 最大文件大小：10MB（可通过环境变量调整）

## API文档

启动后端服务后，可以访问以下地址查看API文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 技术栈

### 后端
- **FastAPI**: 现代、快速的Web框架
- **Uvicorn**: ASGI服务器
- **Pandas**: 数据处理
- **OpenPyXL**: Excel文件生成
- **AutoGen**: LLM集成框架

### 前端
- **Vue 3**: 渐进式JavaScript框架
- **Element Plus**: Vue 3组件库
- **Vite**: 前端构建工具
- **Axios**: HTTP客户端

## 开发说明

### 后端开发
- 主要业务逻辑在 `main.py` 中
- LLM调用基于AutoGen框架
- 支持多种文件编码格式
- 提供完整的错误处理

### 前端开发
- 使用Vue 3 Composition API
- Element Plus提供UI组件
- 支持文件上传和下载
- 实时进度显示

## 部署说明

### 生产环境部署

1. **后端部署**:
```bash
# 使用Gunicorn部署
pip install gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

2. **前端部署**:
```bash
# 构建生产版本
npm run build

# 部署dist目录到Web服务器
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
