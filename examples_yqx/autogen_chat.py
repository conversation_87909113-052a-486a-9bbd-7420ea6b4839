import asyncio

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import ModelClientStreamingChunkEvent
from autogen_agentchat.ui import Console

from llms import model_client

# AssistantAgent这个组件（类）会伴随着项目开发的大部分代码
# 用这个类实现智能体的创建
# 不用OpenAI官方SDK, 也不需要访问调用LLM组件llm.py 而是让更上层的智能体访问model_client实现与LLM的交互

# Define an AssistantAgent with the model, tool, system message, and reflection enabled.
# The system message instructs the agent via natural language.
agent = AssistantAgent(
    name="testcase_agent",
    model_client=model_client,
    system_message="你是一位擅长作诗的助手",
    model_client_stream=True,  # Enable streaming tokens from the model client.
)


# 协程await不能在模块中写，要写在函数体内，并且函数要声明为协程函数

# Non-streaming response
async def main():
    result = await agent.run(task="请写一首四言古诗")  # 等待run方法执行完之后返回结果

    print(result)

# streaming response， 用Generator模拟流式输出，并提取流式输出的content进行拼接
async def main_stream():
    stream = ""
    result = agent.run_stream(task="请写一首四言古诗") # 当前代码不会执行run_stream()中的代码，直接返回协程对象
    async for item in result:
        if isinstance(item, ModelClientStreamingChunkEvent):
            stream += item.content
            # print(stream) 如果在条件判断体里打印拼接，会打印出流式输出的中间结果
    print(stream) # 打印流式输出的最终结果


# Run the agent and stream the messages to the console.
async def main_console():
    await Console(agent.run_stream(task="请写一首四言古诗"))

asyncio.run(main_stream())