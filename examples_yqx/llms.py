import os

from dotenv import load_dotenv
from autogen_core.models import ModelFamily
from autogen_ext.models.openai import OpenAIChatCompletionClient

# 从.env文件中读取环境变量
load_dotenv()


# 定义一个函数get_model_client 来创建模型客户端
def get_model_client():
    # 实例化模型客户端，封装LLM接口
    openai_model_client = OpenAIChatCompletionClient(
        # model="deepseek-chat",
        model=os.getenv("MODEL","deepseek-chat"), # 从环境变量中读取模型名称,如果环境变量中没有配置模型，则默认使用deepseek_chat

        # api_key="***********************************",
        api_key=os.getenv("API_KEY","***********************************"), # 从环境变量中读取API_KEY,如果环境变量中没有配置API_KEY，则默认使用deepseek的API_KEY

        # base_url="https://api.deepseek.com/v1",
        base_url=os.getenv("BASE_URL","https://api.deepseek.com/v1"), # 从环境变量中读取模型地址,如果环境变量中没有配置模型地址，则默认使用siliconflow的地址

        # 添加超时和重试配置
        timeout=60.0,  # 60秒超时
        max_retries=3,  # 最大重试3次

        # 如果没有model_info字段，运行代码报错，根据报错信息增加下面字段
        model_info={
            "vision": False,
            "function_calling": True,
            "json_output": True,
            "family": ModelFamily.UNKNOWN,
            "structured_output": True,
            "multiple_system_messages": True,
        }
    )
    return openai_model_client # 函数最终返回对象openai_model_client

# 单例设计模式
model_client = get_model_client() # 变量model_client接住函数返回的对象，现在它是这个类的一个实例