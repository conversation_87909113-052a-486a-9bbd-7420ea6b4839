# 帮我编写一个函数，统计一段文本中字符的数量
import asyncio
import re

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.base import TaskResult
from autogen_agentchat.messages import ToolCallRequestEvent, ToolCallExecutionEvent, ToolCallSummaryMessage

from examples.llms import model_client


async def count_chinese_characters(text: str) -> int:
    """统计中文字符数量"""
    pattern = re.compile(r'[\u4e00-\u9fa5\u3000-\u303f\uff00-\uffef]')
    chinese_chars = pattern.findall(text)
    return len(chinese_chars)



# Agent call tools
counter_agent = AssistantAgent(
    "counter",
    model_client=model_client,
    system_message="通过调用工具统计测试用例的字数",
    model_client_stream=True, #这个智能体进行流式输出的意义不大
    tools = [count_chinese_characters], # 调用写好的统计字数的函数
    reflect_on_tool_use =True, # 对函数调用的结果进行二次推理（大模型）,使工具调用的结果输出更加符合自然语言表达
)

async def main():
    counter_result = counter_agent.run_stream(task= """用户登录.后端集成: 将已独立调试完成的多智能体功能逻辑，无缝集成到主服务 chat_service 中。
    """)
    async for event in counter_result:
        if isinstance(event, ToolCallRequestEvent): # 发起函数调用的事件
            pass
        if isinstance(event, ToolCallExecutionEvent): # 函数调用完成
            pass
        if isinstance(event,TaskResult): # 函数调用结果
            print(event.messages[-1].content)
        print(event)

asyncio.run(main())

