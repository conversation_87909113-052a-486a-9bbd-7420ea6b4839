import os
import re
import json
import pandas as pd
import openpyxl
import shutil
import threading
import asyncio
from file_processor_agent import FileProcessorAgent
from config_agent import ConfigAgent
from llm_interaction_agent import LLMInteractionAgent

class WorkflowController:
    def __init__(self):
        self.config_agent = ConfigAgent()
        self.file_processor = FileProcessorAgent()
        self.config_agent.load_config()
        self._completed_tasks = 0
        self._total_tasks = 0
        self._task_lock = threading.Lock()

    def _update_progress(self, task_name):
        """更新进度"""
        with self._task_lock:
            self._completed_tasks += 1
            print(f"📊 进度: [{self._completed_tasks}/{self._total_tasks}] {task_name}", flush=True)

    async def _process_single_file(self, input_path, filename, templates, results):
        """异步处理单个文件 - 只处理JSON文件"""
        try:
            if filename.lower().endswith('.json'):
                await self._process_json_file(input_path, filename, templates, results)
            else:
                print(f"跳过非JSON文件: {filename}")
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")

    async def _process_json_file(self, input_path, filename, templates, results):
        """异步处理JSON文件"""
        with open(input_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # 检查JSON数据结构
        if isinstance(json_data, list):
            # 如果是列表，处理每个元素
            for item in json_data:
                if isinstance(item, dict) and "service" in item:
                    await self._process_json_item(item, templates, filename, results)
        elif isinstance(json_data, dict):
            # 如果是字典，直接处理
            if "service" in json_data:
                await self._process_json_item(json_data, templates, filename, results)
        else:
            print(f"文件 {filename} 的JSON格式不支持，跳过处理")

    async def _process_json_item(self, json_item, templates, filename, results):
        """处理单个JSON项目"""
        functional_request_id = json_item.get("functional_request_id", "")
        physical_request_id = json_item.get("physical_request_id", "")
        
        # 准备并发处理所有服务
        service_tasks = []
        for service in json_item["service"]:
            task = self._process_service_block(service, functional_request_id, physical_request_id, templates, filename, results)
            service_tasks.append(task)
        
        await asyncio.gather(*service_tasks)

    async def _process_service_block(self, service, functional_request_id, physical_request_id, templates, filename, results):
        """异步处理服务块"""
        service_id = service.get("service_id", "")
        if service_id.startswith("0x"):
            service_id = service_id[2:]  # Remove the first 2 characters ("0x")
        service_name = service.get("service_name", "")
        subfunctions = service.get("subfunction", [])
        
        # 构建块内容
        block_lines = [
            f"service_id: {service_id}",
            f"service_name: {service_name}",
            f"functional_request_id: {functional_request_id}",
            f"physical_request_id: {physical_request_id}"
        ]
        
        for sub in subfunctions:
            if "response" in sub:
                block_lines.append(f"Subfunction: {sub.get('subfunction_name','')}, ID: {sub.get('subfunction_ID','')}, SPRMB: {sub.get('SPRMB','')}, "
                                f"security_level: {sub.get('security_level','')}, support_session: {','.join(sub.get('support_session',[]))}, "
                                f"function_addression: {sub.get('function_addression','')}, physical_addression: {sub.get('physical_addression','')}, "
                                f"support_NRC: {','.join(sub.get('support_NRC',[]))}, response: {sub.get('response','')}")
            else:
                block_lines.append(f"Subfunction: {sub.get('subfunction_name','')}, ID: {sub.get('subfunction_ID','')}, SPRMB: {sub.get('SPRMB','')}, "
                                f"security_level: {sub.get('security_level','')}, support_session: {','.join(sub.get('support_session',[]))}, "
                                f"function_addression: {sub.get('function_addression','')}, physical_addression: {sub.get('physical_addression','')}, "
                                f"support_NRC: {','.join(sub.get('support_NRC',[]))}")

        # 构建prompt
        if service_id in templates:
            prompt = templates[service_id].format(
                import_precondition="",
                import_markdown_paragraph="\n".join(block_lines)
            )
        else:
            prompt = templates["10"].format(
                import_precondition="",
                import_markdown_paragraph="\n".join(block_lines)
            )
        
        # 注意：这里不调用LLM，只是准备数据
        results.append({
            '原始文件': filename,
            '内容块编号': service_id,
            'Service': service_id,
            '原始内容': prompt
        })

    async def generate_test_cases(self, input_excel_path):
        """异步生成测试用例"""
        print(f"\n🧪 开始生成测试用例...")
        print("=" * 50)
        
        if not os.path.isfile(input_excel_path):
            print(f"❌ 输入文件不存在: {input_excel_path}")
            return
        
        df = pd.read_excel(input_excel_path)
        
        if '原始内容' not in df.columns:
            print("❌ 未找到'原始内容'列，无法处理。")
            return
        
        self._total_tasks = len(df)
        self._completed_tasks = 0
        print(f"📋 准备生成 {self._total_tasks} 个测试用例")
        
        # 创建一个信号量来控制并发数量（避免过多并发导致混乱）
        semaphore = asyncio.Semaphore(3)  # 最多3个并发
        
        async def process_with_semaphore(idx, prompt, task_name):
            async with semaphore:
                result = await self._process_test_case_prompt(idx, prompt, task_name)
                self._update_progress(task_name)
                return result
        
        # 准备并发处理所有提示
        tasks = []
        for idx, row in df.iterrows():
            prompt = row['原始内容']
            task_name = f"测试用例 {idx+1}"
            task = process_with_semaphore(idx, prompt, task_name)
            tasks.append(task)
        
        print(f"\n🚀 开始生成测试用例...")
        # 并发执行所有API调用
        model_reply_list = await asyncio.gather(*tasks)
        
        df['模型答复测试用例'] = model_reply_list
        df.to_excel(input_excel_path, index=False)
        print(f"\n✅ 所有测试用例已生成并保存至: {input_excel_path}")
        return input_excel_path

    async def _process_test_case_prompt(self, idx, prompt, task_name):
        """异步处理单个测试用例提示"""
        async with LLMInteractionAgent(self.config_agent) as llm_agent:
            reply = await llm_agent.call_with_retry(prompt, task_name)
        return reply

    async def process_files(self, input_file_paths, output_folder, template_paths):
        """异步处理文件并生成优化后的提示 - 只处理JSON文件"""
        print(f"\n📁 开始处理文件...")
        print("=" * 50)
        
        # 确保输出文件夹存在
        os.makedirs(output_folder, exist_ok=True)
        
        # 统计JSON文件数量
        json_files = [path for path in input_file_paths if os.path.isfile(path) and path.lower().endswith('.json')]
        print(f"📋 发现 {len(json_files)} 个JSON文件待处理")
        
        if not json_files:
            print("⚠️ 没有找到JSON文件，跳过处理")
            return None
        
        # 异步加载提示模板
        print("📄 加载提示模板中...")
        templates = {}
        template_tasks = []
        for code, path in template_paths.items():
            task = self.file_processor.load_prompt_template(path)
            template_tasks.append((code, task))
        
        template_results = await asyncio.gather(*[task for _, task in template_tasks])
        for (code, _), template in zip(template_tasks, template_results):
            templates[code] = template
        print(f"✅ 已加载 {len(templates)} 个模板")
        
        # 初始化结果列表
        results = []
        
        # 处理每个JSON文件（顺序处理文件，避免过多并发）
        for i, input_path in enumerate(json_files, 1):
            filename = os.path.basename(input_path)
            print(f"📄 [{i}/{len(json_files)}] 处理: {filename}")
            try:
                await self._process_single_file(input_path, filename, templates, results)
                print(f"✅ [{i}/{len(json_files)}] {filename} 处理完成")
            except Exception as e:
                print(f"❌ [{i}/{len(json_files)}] {filename} 处理失败: {str(e)}")
        
        # 保存结果
        print(f"\n💾 保存处理结果...")
        self._save_results(results, output_folder)
        output_path = os.path.join(output_folder, 'Output.xlsx')
        print(f"✅ 文件处理完成，结果已保存至: {output_path}")
        print("=" * 50)
        
        return output_path

    def _save_results(self, results, output_folder):
        """保存结果到Excel"""
        output_path = os.path.join(output_folder, 'Output.xlsx')
          # 检查结果是否为空
        if not results:
            print("⚠️ 没有处理结果，创建空的Excel文件")
            df = pd.DataFrame(columns=['原始文件', '内容块编号', 'Service', '原始内容'])
        else:
            df = pd.DataFrame(results)
            # 确保所有必需的列都存在
            required_columns = ['原始文件', '内容块编号', 'Service', '原始内容']
            for col in required_columns:
                if col not in df.columns:
                    df[col] = ""
            df = df[required_columns]
        
        writer = pd.ExcelWriter(output_path, engine='xlsxwriter')
        df.to_excel(writer, index=False, sheet_name='分析结果')        
        workbook = writer.book
        worksheet = writer.sheets['分析结果']
        worksheet.set_column('A:A', 20)
        worksheet.set_column('B:B', 12)
        worksheet.set_column('C:C', 10)
        worksheet.set_column('D:D', 100)
        writer.close()

    def generate_test_scripts(self, input_excel_path, temp_folder):
        """生成测试脚本（保持同步，因为涉及文件操作）"""
        print(f"\n📋 开始生成测试脚本...")
        print("=" * 50)
        
        if not os.path.isfile(input_excel_path):
            print(f"❌ 输入文件不存在: {input_excel_path}")
            return
        
        output_folder = os.path.dirname(input_excel_path)
        template_file = os.path.join(temp_folder, 'Temp_FR_DCOM_Test.xlsm')
        final_output_file = os.path.join(output_folder, 'Final_Output.xlsm')
        
        try:
            shutil.copy(template_file, final_output_file)
            wb = openpyxl.load_workbook(final_output_file, keep_vba=True)
            df = pd.read_excel(input_excel_path)
            
            if '模型答复测试用例' not in df.columns or 'Service' not in df.columns:
                print("❌ 错误：缺少必要的列")
                return
            
            total_rows = len(df)
            print(f"📊 准备处理 {total_rows} 行数据")
            
            for index, row in df.iterrows():
                text = row['模型答复测试用例']
                service = str(row['Service'])
                testcases = self._parse_testcases_from_text(text)
                self._write_data_to_excel(wb, testcases, service, index)
                print(f"✅ [{index + 1}/{total_rows}] 处理完成: Service {service}")
            
            wb.save(final_output_file)
            print(f"\n✅ 测试脚本已保存至: {final_output_file}")
            print("=" * 50)
            return final_output_file
        
        except Exception as e:
            print(f"❌ 生成测试脚本时出错: {e}")

    def _parse_testcases_from_text(self, text):
        """解析测试用例文本"""
        if not isinstance(text, str):
            return []
        
        items = re.split(r'(?=Description:)', text)
        testcases = []
        
        for item in items:
            if not item.strip():
                continue
            
            testcase = {}
            # 提取描述
            desc_match = re.search(r'Description:\s*"(.*?)"', item)
            if desc_match:
                testcase['Description'] = desc_match.group(1)
            
            # 提取等待时间
            wait_match = re.search(r'TestcaseWait:\s*([^\s]+)', item)
            if wait_match:
                testcase['TestcaseWait'] = wait_match.group(1)
            
            # 提取请求信息（新增Security字段的支持）
            req_match = re.search(
                r'Request:\s*CAN ID:\s*([^\s,]+),\s*Session:\s*([^\s,]+),\s*(Security:\s*([^\s,]+),\s*)?Length:\s*([^\s,]+),\s*ServiceID:\s*([^\s,]+),\s*(Sub-function:\s*([^\n\r]+)|Data:\s*([^\n\r]+))',
                item)
            if req_match:
                testcase['CAN ID'] = req_match.group(1)
                testcase['Session'] = req_match.group(2)
                # group(4) 是 Security 的值（如果有）
                if req_match.group(4):
                    testcase['Security'] = req_match.group(4)
                testcase['Request Len'] = req_match.group(5)
                testcase['Request SID'] = req_match.group(6)
                
                # 处理Sub-function和Data
                data_str = req_match.group(8) if req_match.group(8) else req_match.group(9)
                if data_str:
                    data_list = data_str.strip().split()
                    # 先填Sub-function
                    if len(data_list) > 0:
                        testcase['Sub-function'] = data_list[0]
                    # 再填Data, Data1, Data2, ...，直到Len列前一个
                    len_val = testcase.get('Request Len')
                    try:
                        max_data_fields = int(len_val) - 1 if len_val and len_val.isdigit() else 5
                    except Exception:
                        max_data_fields = 5
                    # Data, Data1, Data2, ..., DataN
                    for i in range(1, max_data_fields):
                        if i < len(data_list):
                            testcase[f'Data{"" if i == 1 else i-1}'] = data_list[i]
                    # 剩余的合并
                    if len(data_list) > max_data_fields:
                        merged = ' '.join(data_list[max_data_fields:])
                        testcase[f'Data{max_data_fields-1}'] = merged
            
            # 提取响应信息
            resp_match = re.search(r'Response:\s*(None|CAN ID:\s*([^\s,]+),\s*Length:\s*([^\s,]+),\s*Service ID:\s*([^\s,]+),\s*Data:\s*([^\n\r]+))', item)
            if resp_match:
                if resp_match.group(1) == "None":
                    testcase.update({
                        'Response Len': None,
                        'Response SID': None,
                        'Response Data': None,
                        'Response CAN ID': None
                    })
                else:
                    testcase.update({
                        'Response CAN ID': resp_match.group(2),
                        'Response Len': resp_match.group(3),
                        'Response SID': resp_match.group(4),
                        'Response Data': resp_match.group(5).strip()
                    })
            
            testcases.append(testcase)
        
        return testcases

    def _write_data_to_excel(self, wb, testcases, service, index):
        """将测试用例写入Excel"""
        field_mapping = {
            "Description": "Discription",
            "TestcaseWait": "TestcaseWait",
            "CAN ID": "CAN ID",
            "Session": "Session",
            "Request Len": "Len",
            "Request SID": "SID",
            "Sub-function": "Sub-function",
            "Data": "Data",
            "Security": "Security",  # 新增Security字段映射
            "Response CAN ID": "Response CAN ID",
            "Response Len": "Response Len",
            "Response SID": "Response SID",
            "Response Data": "Response Data"
        }

        response_len_col = 15
        response_sid_col = 16
        response_data_start_col = 17

        if service not in wb.sheetnames:
            print(f"未找到Sheet：{service}")
            return

        sheet = wb[service]
        headers = [cell.value for cell in sheet[2]]

        # 动态定位Data列索引
        try:
            data_col_idx = headers.index("Data") + 1
        except ValueError:
            data_col_idx = None

        # 动态定位Security列索引（如果有Security列）        
        try:
            security_col_idx = headers.index("Security") + 1
        except ValueError:
            security_col_idx = None

        # 写入测试用例
        for row_idx, testcase in enumerate(testcases, start=2):  # 从第2开始
            # 写入常规字段
            for json_key, excel_header in field_mapping.items():
                if json_key in testcase:
                    try:
                        col_idx = headers.index(excel_header) + 1
                        value = testcase[json_key]
                        if value is not None:
                            sheet.cell(row=row_idx, column=col_idx, value=value)
                    except ValueError:
                        continue

            # 单独写入Security（如果有Security字段且有列）
            if "Security" in testcase and security_col_idx is not None:
                sheet.cell(row=row_idx, column=security_col_idx, value=testcase["Security"])

            # 写入Data1~Data5
            if data_col_idx is not None:
                for i in range(1, 6):
                    key = f"Data{i}"
                    if key in testcase:
                        sheet.cell(row=row_idx, column=data_col_idx + i, value=testcase[key])

            # 写入响应字段
            if "Response Len" in testcase:
                sheet.cell(row=row_idx, column=response_len_col, value=testcase["Response Len"])
            if "Response SID" in testcase:
                sheet.cell(row=row_idx, column=response_sid_col, value=testcase["Response SID"])
            if "Response Data" in testcase:
                data_parts = self._clean_and_split_data(testcase["Response Data"])
                if data_parts:
                    for i in range(min(5, len(data_parts))):
                        sheet.cell(row=row_idx, column=response_data_start_col + i, value=data_parts[i].upper())
                    if len(data_parts) > 5:
                        merged = ''.join(data_parts[5:]).upper()
                        sheet.cell(row=row_idx, column=response_data_start_col + 5, value=merged)

            # 标记为自动测试
            sheet.cell(row=row_idx, column=25, value="AutoTest")

    def _clean_and_split_data(self, response_data):
        """清洗和分割响应数据"""
        if isinstance(response_data, str):
            if response_data.strip().upper() == "NONE":
                return None
            if "0x" in response_data:
                return re.findall(r'0x([0-9A-Fa-f]{2})', response_data)
            return response_data.strip().split()
        elif isinstance(response_data, (list, tuple)):
            if all(isinstance(x, int) for x in response_data):
                return [f"0x{x:02X}" for x in response_data]
            return self._clean_and_split_data(str(response_data))
        return []