/
根据提供的Markdown信息和提示词模板，帮我生成提示词：

以下是提示词模板：
<提示词模板>
""" 以以下格式整理出UDS诊断(填入服务名称)服务的所有测试条码，要求包含： 
1. 会话类型：默认会话,扩展会话, 博世会话(60) 
2. 寻址模式：功能寻址和物理寻址
3. 正响应和负响应(NRC) 
4. 支持的负响应(NRC)：12, 13. 每个负响应要测5次不同的输入 
5. 每次测试之前先清除DTC 
6. 每个测试条码包含完整字段（英文描述内容）：Description, TestcaseWait, Request, Response

具体格式要求： 
Description: "描述文本" 
TestcaseWait: 等待时间(ms) 
Request: CAN ID: ..., Session: ..., Security: ..., Length: ..., ServiceID: ..., Sub-function: ... 
Response: CAN ID: ..., Length: ..., Service ID: ..., Data: ... (或None) 

特别说明： 
- 子功能00表示请求响应（ECU回复7E 00） 
- 子功能80表示抑制响应（ECU不回复） 
- 无效子功能应返回NRC 12 
- 错误报文长度应返回NRC 13 

示例格式： 
Description: "Clear DTCs" 
TestcaseWait: 50 ms 
Request: CAN ID: 7DF, Session: 1001, Security: None, Length: 04, ServiceID: 14, Sub-function: FF FF FF 
Response: CAN ID: 7E8, Length: 01, Service ID: 54, Data: None 

Description: "Check 3E service in extended session with sub-function 80" 
TestcaseWait: 50 ms 
Request: CAN ID: 7E0, Session: 1003, Security: None, Length: 02, ServiceID: 3E, Sub-function: 80 
Response: None  # 明确表示无响应 """
</提示词模板>

md文件内容：
{import_precondition}
{import_markdown_paragraph}
/