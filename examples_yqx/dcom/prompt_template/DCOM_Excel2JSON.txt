/
根据提供的JSON模板结构和markdown文件内容，请将markdown中的信息准确转换为符合JSON模板格式的数据。具体要求如下：

1. 严格按照给定的JSON模板结构进行转换
2. 确保所有字段名称和层级关系与模板完全一致
3. JSON根对象必须包含以下基础字段（请根据markdown内容中的实际信息更新）：
   - "name": "UDS_Questionnaire"（如果markdown中有具体项目名称，请使用实际名称）
   - "functional_request_id": "7DF"（请根据markdown中的功能请求ID更新，如果没有则使用默认值）
   - "physical_request_id": "717"（请根据markdown中的物理请求ID更新，如果没有则使用默认值）
   - "service": [服务数组]

4. 对于markdown中的每个服务(service)，需要包含：
   - service_id
   - service_name
   - subfunction数组（包含所有子功能）

5. 每个子功能(subfunction)必须包含以下字段：
   - subfunction_name
   - subfunction_ID（十六进制格式）
   - SPRMB（"yes"/"no"）
   - security_level（"no"或具体级别如"level_1"）
   - support_session（会话支持列表）
   - function_addression（"yes"/"no"）
   - physical_addression（"yes"/"no"）
   - support_NRC（支持的否定响应码列表）

6. 特别注意：
   - 仔细查找markdown中的项目名称、功能请求ID、物理请求ID等基础信息并准确填入
   - 如果找到具体的CAN ID或请求ID信息，请替换默认的"7DF"和"717"
   - 保持所有字段的命名和大小写与模板一致
   - 确保数值型ID转换为字符串类型
   - 十六进制值保持"0x"前缀
   - 列表项使用双引号包裹
   - 预置条件(precondition)信息如果存在，请作为上下文参考

7. JSON结构示例：
```json
  "name": "UDS_Questionnaire",
  "functional_request_id": "7DF",
  "physical_request_id": "717",
  "service": [
      "service_id": "0x10",
      "service_name": "DiagnosticSessionControl",
      "subfunction": [...]
  ]
```

8. 信息提取优先级：
   - 优先从markdown的标题、表头或明确标识的字段中提取项目信息
   - 查找包含"CAN ID"、"Request ID"、"Functional"、"Physical"等关键词的内容
   - 如果无法找到具体信息，使用默认值并在字段后标注"[使用默认值]"

请检查生成的JSON是否满足：
- 结构完整性
- 字段完整性
- 数据准确性
- 格式规范性
- 基础信息的准确性

如果markdown中有任何信息缺失，请标注"[待补充]"并继续处理其他可用数据。

以下是markdown文件内容：
{import_ExcelMarkdown}

/