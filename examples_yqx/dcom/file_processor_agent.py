import openpyxl
import tempfile
from markitdown import MarkItDown


# ====================
# 文件处理代理 (FileProcessorAgent)
# ====================
class FileProcessorAgent:
    @staticmethod
    async def load_prompt_template(template_path):
        """异步加载提示模板"""
        try:
            import aiofiles
            async with aiofiles.open(template_path, 'r', encoding='utf-8') as file:
                return await file.read()
        except Exception as e:
            print(f"加载提示模板失败: {str(e)}")
            return None

    @staticmethod
    def convert_to_md(file_path):
        """将文件转换为Markdown格式"""
        md = MarkItDown(enable_plugins=False)
        result = md.convert(file_path)
        return result.text_content.split('\n')

    @staticmethod
    def excel_to_tmp_xlsx_all_sheets(input_xlsx_path):
        """处理Excel合并单元格并生成临时文件"""
        wb = openpyxl.load_workbook(input_xlsx_path)
        data_dict = {}
        
        for sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            data = []
            for row in ws.iter_rows():
                new_row = [cell.value if cell.value is not None else '' for cell in row]
                data.append(new_row)
            
            # 填充合并单元格
            for merged_range in ws.merged_cells.ranges:
                min_col, min_row, max_col, max_row = merged_range.bounds
                top_left_value = str(ws.cell(row=min_row, column=min_col).value or '')
                for r in range(min_row, max_row + 1):
                    for c in range(min_col, max_col + 1):
                        if not (r == min_row and c == min_col):
                            data[r - 1][c - 1] = top_left_value
            data_dict[sheet_name] = data

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as tmpfile:
            tmp_filename = tmpfile.name
        
        new_wb = openpyxl.Workbook()
        default_sheet = new_wb.active
        new_wb.remove(default_sheet)
        
        for sheet_name, data in data_dict.items():
            new_ws = new_wb.create_sheet(title=sheet_name)
            for row_index, row_data in enumerate(data):
                for col_index, cell_value in enumerate(row_data):
                    new_ws.cell(row=row_index + 1, column=col_index + 1, value=cell_value)
        
        new_wb.save(tmp_filename)
        return tmp_filename

    @staticmethod
    def extract_content_sections(content):
        """从Markdown内容中提取各个部分"""
        sections = {
            "precondition": [],
            "DID": [],
            "DTC": [],
            "Snapshot": [],
            "ServiceHeader": [],
            "Service": []
        }
        
        # ## GeneralInfo Sheet
        precondition_start = -1
        next_section_after_generalinfo = len(content)

        # 查找 GeneralInfo 区段的开始与结束
        for i, line in enumerate(content):
            if line.strip().startswith("## GeneralInfo"):
                precondition_start = i
            elif precondition_start != -1 and line.strip().startswith("## Service") and not line.strip().startswith("## GeneralInfo"):
                next_section_after_generalinfo = i
                break

        # 提取 GeneralInfo 到下一个 ## 之间的内容
        if precondition_start != -1 and precondition_start < next_section_after_generalinfo:
            sections["precondition"] = content[precondition_start + 1: next_section_after_generalinfo]

        # 提取 DID 区块
        did_start = -1
        next_section_after_did = len(content)
        
        for i, line in enumerate(content):
            if line.strip().startswith("## DID"):
                did_start = i
            elif did_start != -1 and line.strip().startswith("##") and not line.strip().startswith("## DID"):
                next_section_after_did = i
                break

        if did_start != -1 and did_start < next_section_after_did:
            sections["DID"] = content[did_start + 1: next_section_after_did]
        
        # 提取 DTC 区块
        dtc_start = -1
        next_section_after_dtc = len(content)

        for i, line in enumerate(content):
            if line.strip().startswith("## DTC"):
                dtc_start = i
            elif dtc_start != -1 and line.strip().startswith("##") and not line.strip().startswith("## DTC"):
                next_section_after_dtc = i
                break

        if dtc_start != -1 and dtc_start < next_section_after_dtc:
            sections["DTC"] = content[dtc_start + 1: next_section_after_dtc]

        # 提取 Snapshot 区块
        snapshot_start = -1
        next_section_after_snapshot = len(content)

        for i, line in enumerate(content):
            if line.strip().startswith("## Snapshot"):
                snapshot_start = i
            elif snapshot_start != -1 and line.strip().startswith("##") and not line.strip().startswith("## Snapshot"):
                next_section_after_snapshot = i
                break

        if snapshot_start != -1 and snapshot_start < next_section_after_snapshot:
            sections["Snapshot"] = content[snapshot_start + 1: next_section_after_snapshot]
        
        # 找到"###Service"行的位置
        service_start = -1
        next_section_start = len(content)
        
        for i, line in enumerate(content):
            if line.strip().startswith("## Service"):
                service_start = i
            elif service_start != -1 and line.strip().startswith("##") and not line.strip().startswith("## Service"):
                next_section_start = i
                break
        
        if service_start != -1 and service_start < next_section_start:
            sections["Service"] = content[service_start + 1: next_section_start]

        # 提取表头为## Service行后第3、4行
        if service_start != -1 and service_start + 5 < len(content):
            sections["ServiceHeader"] = '\n'.join([content[service_start + 3], content[service_start + 4]])
        else:
            sections["ServiceHeader"] = ""

        return sections