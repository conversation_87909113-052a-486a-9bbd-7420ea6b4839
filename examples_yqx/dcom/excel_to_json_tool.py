import os
import json
import pandas as pd
from config_agent import ConfigAgent
from file_processor_agent import FileProcessorAgent
from llm_interaction_agent import LLMInteractionAgent

class ExcelToJSONTool:
    """工具类：读取Excel转为Markdown，叠加prompt并请求大模型得到JSON"""
    def __init__(self, config_agent=None, file_processor=None):
        self.config_agent = config_agent or ConfigAgent()
        self.file_processor = file_processor or FileProcessorAgent()
        self.config_agent.load_config()

    async def convert_and_call_llm(self, input_file_paths, template_path, output_folder):
        """异步转换并调用LLM"""
        os.makedirs(output_folder, exist_ok=True)
        output_records = []
        
        # 准备处理任务
        tasks = []
        for input_path in input_file_paths:
            if not input_path.lower().endswith('.xlsx'):
                continue
            if not os.path.isfile(input_path):
                continue
            
            filename = os.path.basename(input_path)
            task = self._process_excel_file(input_path, filename, template_path)
            tasks.append(task)
        
        # 并发处理所有Excel文件
        import asyncio
        results = await asyncio.gather(*tasks)
        output_records.extend([result for result in results if result])
        
        # 写入Output_UDS_Questionnaire.xlsx
        if output_records:
            output_path = os.path.join(output_folder, "Output_UDS_Questionnaire.xlsx")
            pd.DataFrame(output_records).to_excel(output_path, index=False)
            print(f"所有答复已写入 {output_path}")

    async def _process_excel_file(self, input_path, filename, template_path):
        """异步处理单个Excel文件"""
        tmp_xlsx = self.file_processor.excel_to_tmp_xlsx_all_sheets(input_path)
        content_lines = self.file_processor.convert_to_md(tmp_xlsx)
        os.remove(tmp_xlsx)

        # 提取内容部分
        sections = self.file_processor.extract_content_sections(content_lines)
        
        # 异步加载提示模板
        prompt_template = await self.file_processor.load_prompt_template(template_path)
        if prompt_template is None:
            print(f"❌ 加载模板失败: {template_path}")
            return None
            
        prompt = prompt_template.format(
            import_ExcelMarkdown="\n".join(sections.get("precondition", []) + sections.get("Service", []))
        )

        print(f"📄 {filename} 处理中...")
        print("=" * 50)
        print("🔍 生成的Prompt:")
        print(prompt)
        print("=" * 50)
        
        print(f"🚀 {filename} 请求大模型中...")
        async with LLMInteractionAgent(self.config_agent) as llm_agent:
            result = await llm_agent.call_with_retry(prompt, f"Excel转JSON-{filename}")
        
        print("📋 大模型返回:")
        print("=" * 50)
        print(result)
        print("=" * 50)
        
        return {
            "文件名": filename,
            "Prompt": prompt,
            "模型答复": result
        }

    def extract_json_from_model_reply(self, input_excel, output_json_folder):
        """
        读取模型答复列内容，提取JSON内容，写入新列，并生成JSON文件
        """
        print(f"\n📊 开始提取JSON内容...")
        print("=" * 50)
        
        # 读取Excel
        df = pd.read_excel(input_excel)
        if '模型答复' not in df.columns:
            print("❌ 未找到'模型答复'列，无法处理。")
            return

        json_entry_list = []
        all_json_data = []

        for idx, reply in enumerate(df['模型答复']):
            print(f"📄 [{idx + 1}/{len(df)}] 处理模型答复...")
            
            analysis_result = str(reply)
            json_start = analysis_result.find('```json\n')
            if json_start == -1:
                json_start = analysis_result.find('[')
            else:
                json_start += len('```json\n')

            json_end = analysis_result.rfind('```')
            if json_end == -1:
                json_end = analysis_result.rfind(']') + 1

            if json_start != -1 and json_end != -1 and json_end > json_start:
                json_str = analysis_result[json_start:json_end].strip()
                try:
                    json_data = json.loads(json_str)
                    json_entry_list.append(json.dumps(json_data, ensure_ascii=False))
                    all_json_data.append(json_data)
                    print(f"✅ [{idx + 1}/{len(df)}] JSON解析成功")
                except json.JSONDecodeError as e:
                    print(f"❌ [{idx + 1}/{len(df)}] JSON解析失败: {str(e)}")
                    print(f"🔍 尝试解析的内容: {json_str[:200]}...")
                    json_entry_list.append("")
            else:
                print(f"⚠️ [{idx + 1}/{len(df)}] 未能找到JSON内容")
                json_entry_list.append("")

        # 写入新列
        df['提取的JSON'] = json_entry_list
        df.to_excel(input_excel, index=False)
        print(f"✅ 已将提取的JSON写入 {input_excel}")

        # 保存所有JSON到文件
        os.makedirs(output_json_folder, exist_ok=True)
        json_output_path = os.path.join(output_json_folder, "Excel2JSON_UDS_Questionnaire.json")
        
        # 展平JSON数据
        flattened_json = []
        for json_data in all_json_data:
            if isinstance(json_data, list):
                flattened_json.extend(json_data)
            else:
                flattened_json.append(json_data)
        
        with open(json_output_path, "w", encoding="utf-8") as f:
            json.dump(flattened_json, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 所有JSON已写入 {json_output_path}")
        print(f"📊 总共提取了 {len(flattened_json)} 个JSON对象")
        print("=" * 50)