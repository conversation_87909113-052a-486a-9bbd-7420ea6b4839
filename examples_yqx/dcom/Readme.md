# DCOM_AI 软件架构解析

## 1. 总体架构说明

本项目主要用于汽车诊断协议（UDS）相关的问卷解析、Prompt生成、测试用例生成及自动化脚本输出。整体架构采用多代理分层设计，核心分为配置管理、文件处理、模型交互、业务流程控制和Excel转JSON工具五大模块。

---

## 2. 各模块功能解析

### 2.1 ConfigAgent（基础配置代理）

- 负责加载环境变量、API密钥、代理配置等基础信息。
- 支持多种API（如 Bosch BD、DeepSeek、SiliconFlow、本地API）。
- 提供统一的请求头和鉴权令牌获取接口。

### 2.2 FileProcessorAgent（文件处理代理）

- 负责加载Prompt模板、Excel/Markdown文件转换、合并单元格处理。
- 支持从Markdown内容中提取各个业务区块（如GeneralInfo、DID、DTC、Snapshot、Service等）。
- 提供辅助工具函数，便于后续业务流程调用。

### 2.3 LLMInteractionAgent（大模型交互代理）

- 封装与各类大模型API的交互逻辑，包括请求、重试、加载动画等。
- 支持多种API调用方式，自动处理响应内容的解析。
- 提供带重试和进度提示的调用接口，提升用户体验。

### 2.4 WorkflowController（工作流控制器）

- 业务主控模块，负责整体流程的串联和调度。
- 主要功能包括：
  - `process_files`：处理输入文件，生成优化后的Prompt。
  - `generate_test_cases`：根据优化后的Prompt生成测试用例。
  - `generate_test_scripts`：将测试用例写入Excel脚本模板，生成最终自动化测试脚本。
- 内部包含服务块提取、结果保存、测试用例解析等辅助方法。

### 2.5 ExcelToJSONTool（Excel转JSON工具）

- 用于将Excel问卷内容转换为Markdown，叠加Prompt并请求大模型，最终提取JSON结构化数据。
- 支持批量处理Excel文件，自动生成JSON文件，便于后续自动化处理。

---

## 3. 主业务流程（main函数）解析

主程序入口为 `main()`，用户可通过命令行交互选择不同功能：

1. **DCOM测试用例生成**
   - 支持输入数据类型选择（JSON或Excel）。
   - 支持执行模式选择：
     - 全量自动执行：自动完成所有流程（Prompt生成、测试用例生成、脚本生成）。
     - 全量监督执行：每步人工检查确认后再继续。
     - 仅优化Prompt：只生成优化后的Prompt。
     - 仅生成测试用例：只生成测试用例。
     - 仅测试脚本生成：只生成自动化测试脚本。
   - 各流程均由 `WorkflowController` 调度完成，结果自动保存并打开。

2. **诊断问卷格式转换**
   - 批量处理Excel问卷，生成Markdown并请求大模型，提取结构化JSON数据。
   - 由 `ExcelToJSONTool` 完成，自动输出JSON文件。

---


## 4. 结语

本架构设计充分考虑了模块解耦、扩展性和自动化需求，便于后续维护和功能拓展。各模块职责清晰，主流程灵活可控，适合大规模汽车诊断协议相关的自动化处理场景。