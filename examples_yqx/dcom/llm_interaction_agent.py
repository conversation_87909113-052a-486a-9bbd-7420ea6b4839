import aiohttp
import asyncio
import threading
import urllib3

# 禁用安全警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# ====================
# LLM交互代理 (LLMInteractionAgent)
# ====================
class LLMInteractionAgent:
    def __init__(self, config_agent):
        self.config_agent = config_agent
        self.connector = None
        self._spinner_active = False
        self._print_lock = threading.Lock()  # 添加打印锁
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(ssl=False)
        self.session = aiohttp.ClientSession(connector=connector)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def call_api(self, prompt):
        """异步调用大模型API"""
        payload = {
            "model": self.config_agent.gpt_model,
            "messages": [
                {"role": "system", "content": "你是一个专业的汽车诊断协议分析专家。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 4000
        }
        
        try:
            if self.config_agent.selected_api == "BD_API_URL":
                async with self.session.post(
                    "https://aigc.bosch.com.cn/llmservice/api/v1/chat/messages",
                    headers=self.config_agent.get_headers(),
                    json=payload,
                    proxy=self.config_agent.proxies.get('https') if self.config_agent.proxies else None
                ) as response:
                    response.raise_for_status()
                    return await response.json()
            
            elif self.config_agent.selected_api == "DEEPSEEK_API_URL":
                async with self.session.post(
                    "https://api.deepseek.com/v1/chat/completions",
                    headers=self.config_agent.get_headers(),
                    json=payload,
                    proxy=self.config_agent.proxies.get('https') if self.config_agent.proxies else None
                ) as response:
                    response.raise_for_status()
                    return await response.json()
            
            elif self.config_agent.selected_api == "siliconflow_API_URL":
                async with self.session.post(
                    "https://api.siliconflow.cn/v1/chat/completions",
                    headers=self.config_agent.get_headers(),
                    json=payload,
                    proxy=self.config_agent.proxies.get('https') if self.config_agent.proxies else None
                ) as response:
                    response.raise_for_status()
                    return await response.json()
            
            elif self.config_agent.selected_api == "Local_API_URL":
                async with self.session.post(
                    "http://10.178.239.202:5000/v1/chat/completions",
                    headers=self.config_agent.get_headers(),
                    json=payload
                ) as response:
                    response.raise_for_status()
                    return await response.json()
        
        except aiohttp.ClientError as e:
            print(f"API调用失败: {str(e)}")
            return None
    
    async def call_with_spinner(self, prompt, task_name=""):
        """带加载动画的异步API调用"""
        # 创建一个任务来执行API调用
        api_task = asyncio.create_task(self.call_api(prompt))
        
        try:
            # 等待API调用完成（不显示旋转动画，避免并发冲突）
            result = await api_task
            return result
        finally:
            pass
    
    def _safe_print(self, message):
        """线程安全的打印"""
        with self._print_lock:
            print(message, flush=True)
    
    async def call_with_retry(self, prompt, task_name=""):
        """带重试机制的异步API调用"""
        for attempt in range(self.config_agent.max_retries):
            try:
                if attempt == 0:
                    self._safe_print(f"🚀 {task_name} 开始处理...")
                else:
                    self._safe_print(f"🔄 {task_name} 第 {attempt+1} 次重试...")
                
                api_response = await self.call_with_spinner(prompt, task_name)
                if api_response is None:
                    raise Exception("API调用返回None")
                
                # 提取分析结果
                if self.config_agent.selected_api == "BD_API_URL":
                    result = api_response['data']['messages'][0]['content']
                elif self.config_agent.selected_api in ["DEEPSEEK_API_URL", "siliconflow_API_URL", "Local_API_URL"]:
                    result = api_response['choices'][0]['message']['content']
                
                self._safe_print(f"✅ {task_name} 完成")
                return result
                
            except Exception as e:
                if attempt < self.config_agent.max_retries - 1:
                    self._safe_print(f"❌ {task_name} 失败，准备重试: {str(e)}")
                    await asyncio.sleep(self.config_agent.retry_delay)
                else:
                    self._safe_print(f"❌ {task_name} 最终失败: {str(e)}")
        
        return f"API调用失败（共重试 {self.config_agent.max_retries} 次）"