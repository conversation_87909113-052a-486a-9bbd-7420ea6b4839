import os
import urllib3
from dotenv import load_dotenv
from urllib.parse import quote

# 禁用安全警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# ====================
# 基础配置代理 (ConfigAgent)
# ====================
class ConfigAgent:
    def __init__(self):
        self.proxies = None
        self.headers = None
        self.selected_api = None
        self.gpt_model = None
        self.max_retries = 3
        self.retry_delay = 1
        
    def load_config(self):
        """加载环境配置"""
        load_dotenv("NT.env")
        
        # 代理配置
        username = os.getenv("USERNAME")
        password = os.getenv("PASSWORD")
        if username and password:
            encoded_password = quote(password)
            proxy = f'http://{username}:{encoded_password}@rb-proxy-unix-de01.bosch.com:8080'
            self.proxies = {"http": proxy, "https": proxy}
        
        # API配置
        self.selected_api = os.getenv("SELECTED_API", "BD_API_URL")
        self.gpt_model = os.getenv("GPT_MODEL", "deepseek-v3")
        
    def get_headers(self):
        """获取API请求头"""
        if self.selected_api == "BD_API_URL":
            return self.get_access_token_header()
        elif self.selected_api in ["DEEPSEEK_API_URL", "siliconflow_API_URL"]:
            api_key = os.getenv(f"{self.selected_api.split('_')[0]}_API_KEY")
            return {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
        elif self.selected_api == "Local_API_URL":
            return {
                "Authorization": "Bearer dummy-key",
                "Content-Type": "application/json"
            }
        return None

    def get_access_token_header(self):
        """获取BD API访问令牌"""
        # 保持同步方式，因为这是配置初始化阶段
        import requests
        url = "https://login.microsoftonline.com/0ae51e19-07c8-4e4b-bb6d-648ee58410f4/oauth2/v2.0/token"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "client_id": "d22a966a-c100-4025-b09b-487c114079a1",
            "client_secret": "****************************************",
            "scope": "d22a966a-c100-4025-b09b-487c114079a1/.default",
            "grant_type": "client_credentials"
        }

        response = requests.post(url, headers=headers, data=data, proxies=self.proxies, verify=False)
        if response.status_code == 200:
            token_data = response.json()
            return {"Authorization": f"{token_data['token_type']} {token_data['access_token']}"}
        print(f"获取BD API权限失败: {response.status_code}")
        return None