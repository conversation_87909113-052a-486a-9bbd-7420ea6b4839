[{"name": "FCR", "functional_request_id": "0x7DF", "physical_request_id": "0x717", "service": [{"service_id": "0x10", "service_name": "DiagnosticSessionControl", "subfunction": [{"subfunction_name": "Default Session", "subfunction_ID": "0x01", "SPRMB": "yes", "security_level": "no", "support_session": ["$01 Default", "$03 Extended", "$01 Default", "$02 Programming", "$03 Extended"], "function_addression": "yes", "physical_addression": "yes", "support_NRC": ["12", "13"]}, {"subfunction_name": "ProgrammingSession", "subfunction_ID": "0x02", "SPRMB": "no", "security_level": "level_1", "support_session": ["$03 Extended", "$02 Programming"], "function_addression": "no", "physical_addression": "yes", "support_NRC": ["12", "13", "22", "7E"], "comments": "Voltage is out of range 9v to 32v; Vehicle Speed >3 km/h; 档位不处于P档;"}, {"subfunction_name": "Extended Session", "subfunction_ID": "0x03", "SPRMB": "yes", "security_level": "no", "support_session": ["$01 Default", "$03 Extended", "$01 Default"], "function_addression": "yes", "physical_addression": "yes", "support_NRC": ["12", "13", "7E"], "comments": "/"}]}]}]