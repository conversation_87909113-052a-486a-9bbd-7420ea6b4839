import os
import async<PERSON>
from workflow_controller import WorkflowController
from excel_to_json_tool import ExcelToJSONTool

async def main():
    input_folder = 'CusQuestionaire_JSON'
    output_folder = 'Output'
    temp_folder = 'Temp'
    template_paths = {
        "10": os.path.join('prompt_template', 'DCOM_2Stage_10.txt'),
        "11": os.path.join('prompt_template', 'DCOM_2Stage_11.txt'),
        "3E": os.path.join('prompt_template', 'DCOM_2Stage_3E.txt'),
        "22": os.path.join('prompt_template', 'DCOM_2Stage_22.txt'),
        "2E": os.path.join('prompt_template', 'DCOM_2Stage_2E.txt'),
        "27": os.path.join('prompt_template', 'DCOM_2Stage_27.txt'),
        "14": os.path.join('prompt_template', 'DCOM_2Stage_14.txt'),
        "19": os.path.join('prompt_template', 'DCOM_2Stage_19.txt'),
        "85": os.path.join('prompt_template', 'DCOM_2Stage_85.txt'),
        "28": os.path.join('prompt_template', 'DCOM_2Stage_28.txt'),
        "31": os.path.join('prompt_template', 'DCOM_2Stage_31.txt'),
        "00": os.path.join('prompt_template', 'DCOM_2Stage_00.txt')
    }
    
    # 获取JSON输入文件路径列表
    input_file_paths = []
    for filename in os.listdir(input_folder):
        if filename.lower().endswith('.json') and os.path.isfile(os.path.join(input_folder, filename)):
            input_file_paths.append(os.path.join(input_folder, filename))
    
    controller = WorkflowController()
    print(f"当前配置为API：{controller.config_agent.selected_api} 模型：{controller.config_agent.gpt_model}")
    print("请选择执行模式：")
    print("1. JSON全量执行（无人工检查，自动全流程）")
    print("2. JSON全量监督执行（每步人工检查）")
    print("3. Excel全量执行（无人工检查，自动全流程）")
    print("4. 生成测试用例（只执行generate_test_cases）")
    print("5. 测试脚本生成（只执行generate_test_scripts）")
    print("6. 诊断问卷格式转换")
    mode = input("请输入数字选择（1-6）：").strip()
    
    if mode == "1":
        await controller.process_files(input_file_paths, output_folder, template_paths)
        await controller.generate_test_cases(os.path.join(output_folder, 'Output.xlsx'))
        controller.generate_test_scripts(os.path.join(output_folder, 'Output.xlsx'), temp_folder)
        print("全量自动执行完成。")
        os.startfile(os.path.join(output_folder, 'Output.xlsx'))
        os.startfile(os.path.join(output_folder, 'Final_Output.xlsm'))
    elif mode == "2":
        await controller.process_files(input_file_paths, output_folder, template_paths)
        os.startfile(os.path.join(output_folder, 'Output.xlsx'))
        input("请检查Output.xlsx，确认优化后的Prompt无误后按回车继续...")
        await controller.generate_test_cases(os.path.join(output_folder, 'Output.xlsx'))
        os.startfile(os.path.join(output_folder, 'Output.xlsx'))
        input("请检查生成的测试用例无误后按回车继续...")
        controller.generate_test_scripts(os.path.join(output_folder, 'Output.xlsx'), temp_folder)
        print("全量监督执行完成。")
        os.startfile(os.path.join(output_folder, 'Final_Output.xlsm'))
    elif mode == "3":
        print(f"\n📊 开始Excel全量执行...")
        print("=" * 50)
        print("第一步：诊断问卷格式转换...")
        
        # 第一步：执行诊断问卷格式转换
        excel_input_folder = 'CusQuestionaire'
        output_json_folder = 'CusQuestionaire'
        template_path = os.path.join('prompt_template', 'DCOM_Excel2JSON.txt')
        
        # 获取Excel输入文件路径列表
        excel_file_paths = []
        for filename in os.listdir(excel_input_folder):
            if filename.lower().endswith('.xlsx') and os.path.isfile(os.path.join(excel_input_folder, filename)):
                excel_file_paths.append(os.path.join(excel_input_folder, filename))
        
        if not excel_file_paths:
            print("❌ 在CusQuestionaire文件夹中未找到Excel文件")
            return
        
        tool = ExcelToJSONTool()
        await tool.convert_and_call_llm(excel_file_paths, template_path, output_folder)
        tool.extract_json_from_model_reply(
            input_excel=os.path.join(output_folder, 'Output_UDS_Questionnaire.xlsx'), 
            output_json_folder=output_json_folder
        )
        print("✅ 诊断问卷格式转换完成！")
        
        # 第二步：使用转换后的JSON文件执行完整流程
        print("第二步：执行完整流程...")
        
        # 重新获取转换后的JSON文件路径列表
        converted_json_paths = []
        for filename in os.listdir(output_json_folder):
            if filename.lower().endswith('.json') and os.path.isfile(os.path.join(output_json_folder, filename)):
                converted_json_paths.append(os.path.join(output_json_folder, filename))
        
        if not converted_json_paths:
            print("❌ 转换后未找到JSON文件")
            return
        
        # 执行与模式1相同的完整流程
        await controller.process_files(converted_json_paths, output_folder, template_paths)
        await controller.generate_test_cases(os.path.join(output_folder, 'Output.xlsx'))
        controller.generate_test_scripts(os.path.join(output_folder, 'Output.xlsx'), temp_folder)
        print("✅ Excel全量自动执行完成！")
        os.startfile(os.path.join(output_folder, 'Output.xlsx'))
        os.startfile(os.path.join(output_folder, 'Final_Output.xlsm'))
    elif mode == "4":
        await controller.generate_test_cases(os.path.join(output_folder, 'Output.xlsx'))
        print("生成测试用例执行完成。")
        os.startfile(os.path.join(output_folder, 'Output.xlsx'))
    elif mode == "5":
        controller.generate_test_scripts(os.path.join(output_folder, 'Output.xlsx'), temp_folder)
        print("测试脚本生成执行完成。")
        os.startfile(os.path.join(output_folder, 'Output.xlsx'))
        os.startfile(os.path.join(output_folder, 'Final_Output.xlsm'))
    elif mode == "6":
        print(f"\n📊 开始诊断问卷格式转换...")
        print("=" * 50)
        excel_input_folder = 'CusQuestionaire'
        output_json_folder = 'CusQuestionaire'
        template_path = os.path.join('prompt_template', 'DCOM_Excel2JSON.txt')
        
        # 获取Excel输入文件路径列表
        excel_file_paths = []
        for filename in os.listdir(excel_input_folder):
            if filename.lower().endswith('.xlsx') and os.path.isfile(os.path.join(excel_input_folder, filename)):
                excel_file_paths.append(os.path.join(excel_input_folder, filename))
        
        tool = ExcelToJSONTool()
        await tool.convert_and_call_llm(excel_file_paths, template_path, output_folder)
        tool.extract_json_from_model_reply(
            input_excel=os.path.join(output_folder, 'Output_UDS_Questionnaire.xlsx'), 
            output_json_folder=output_json_folder
        )
        print("✅ 诊断问卷格式转换完成！")
        os.startfile(os.path.join(output_folder, 'Output_UDS_Questionnaire.xlsx'))
    else:
        print("无效选择，请输入1-6。")

if __name__ == "__main__":
    asyncio.run(main())
