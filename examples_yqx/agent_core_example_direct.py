"""
耦合度低的消息驱动的智能体之间的交互
直接消息方式：send_message()
广播消息方式z: publish_message() ——更常见，推荐
借助Code Assistant实现一个生成级应用要参考的逻辑：请参考代码逻辑帮我实现一个xxx业务流
参考代码逻辑的完整度决定了最终生成的项目代码的质量
"""
import asyncio
from dataclasses import dataclass

from autogen_core import MessageContext, message_handler, RoutedAgent, AgentId
from pydantic import BaseModel



@dataclass
class MessageA:
    msg: str

# 推荐
class MessageB(BaseModel):
     task: str

# a = MessageA("hello")
#b = MessageB()

class ImageAnalysisAgent(RoutedAgent):
    # def __init__(self) -> None: # ->None是类型提示，表示这个方法执行完毕后不返回任何值
    #     # super()是python的内置函数，表示在子类的__init__方法中调用父类的__init__方法
    #     # 子类通常有自己独特的属性，但它也必须确保父类的属性被正确初始化
    #     super().__init__("图片分析智能体")
    pass

    # @Message_handler是一个典型的注册型装饰器，它的作用是将“事件”（收到某种消息MessageB）和
    # ”响应“函数（处理该消息的方法handle_my_message）优雅的关联起来，实现模块之间的解耦和异步通信
    @message_handler
    async def handle_my_message(self, message: MessageB, ctx: MessageContext) -> None:
        # 这只是一个框架，handle_my_message里面没有任何业务逻辑，也没有任何方法
        # 需要自己实现业务逻辑：做分析、调用llm、调用MCP、用LlamaIndex、用Langchain等
        print(message.task)
        print("图片分析已完成")


        # 图片分析完成之后调用用例生成智能体，运行时发出信息：runtime.send_message();智能体发出信息：self.send_message()
        # MessageA(msg="图片分析已完成，请开始生成用例")
        await self.send_message(MessageA(msg="图片分析已完成，请开始生成用例"), AgentId("agent2", "default"))


class TestCaseGenerationAgent(RoutedAgent):
    def __init__(self) -> None:
        super().__init__(description="用例生成智能体")

    @message_handler
    async def handle_my_message(self, message: MessageA, ctx: MessageContext) -> None:
        print(message.msg)
        print("用例生成已完成")

    # 当在一个类中定义了两个同名的方法（这里都是handle_my_message）,后一个定义会完全覆盖前一个定义
    # 导致TestCaseGenerationAgent类最终只拥有一个能处理MessageB类型消息的handle_my_message方法
    # 那个本来用来处理MessageA类型消息的方法在类被定义时丢失
    # 当agent1向agent2发送MessageA消息时，agent2内没有对应的处理器，导致交互链条中断
    @message_handler
    async def handle_my_message2(self, message: MessageB, ctx: MessageContext) -> None:
        print(message.task)
        print("用例生成已完成22")


# 前面定义的2个智能体都继承了BaseAgent
# 如何让代码的执行先运行 class ImageAnalysisAgent 里面的 async def handle_my_message():
# 然后再执行 class TestCaseGenerationAgent 里面的 async def handle_my_message():
# 关键：Agent Runtime Environment 的消息机制驱动智能体之间的交互


async def main():
    # 导入运行时环境
    from autogen_core import SingleThreadedAgentRuntime
    # 创建单线程运行时环境
    runtime = SingleThreadedAgentRuntime()

    # 注册智能体
    # 将ImageAnalysisAgent智能体注册到运行时环境
    # 将ImageAnalysisAgent智能体命名为agent1
    # lambda: ImageAnalysisAgent(description="图片分析智能体")创建ImageAnalysisAgent智能体实例
    await ImageAnalysisAgent.register(runtime, "agent1", lambda: ImageAnalysisAgent(description="图片分析智能体"))
    await TestCaseGenerationAgent.register(runtime, "agent2", lambda: TestCaseGenerationAgent())

    # 运行智能体
    runtime.start()

    await runtime.send_message(MessageB(task="开始分析图片"), AgentId("agent1", "default"))
    # 向哪个智能体发消息，就要确保发送的消息类型与注册装饰器的被装饰方法定义的消息类型一致，向agent2发送MessageB（）类型消息，agent2不会被执行
    # await runtime.send_message(MessageB(task="开始生成用例"), AgentId("agent2", "default"))
    await runtime.stop_when_idle()

asyncio.run(main())
