/* Dashboard页面样式 */
.dashboard-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.welcome-card {
  margin-bottom: 24px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
}

.welcome-card .ant-card-body {
  padding: 32px;
}

.welcome-card .ant-typography {
  color: white !important;
}

.stat-card {
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.stat-card .ant-statistic-title {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.quick-actions-card {
  margin-bottom: 24px;
  border-radius: 12px;
}

.quick-actions-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.quick-actions-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
}

.action-card {
  border-radius: 8px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: 1px solid #f0f0f0;
  height: 180px;
}

.action-card:hover {
  border-color: #1890ff;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.12);
}

.action-content {
  padding: 20px 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.action-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.action-card .ant-typography {
  margin: 0;
}

.feature-card,
.activity-card {
  border-radius: 8px;
}

.feature-card .ant-card-head-title,
.activity-card .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
}

.feature-item:last-child {
  border-bottom: none;
}

.activity-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.activity-content {
  display: flex;
  align-items: center;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .welcome-card .ant-card-body {
    padding: 24px;
  }
}

@media (max-width: 992px) {
  .action-card {
    height: 160px;
  }
  
  .action-content {
    padding: 16px 12px;
  }
  
  .action-icon {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 12px;
  }
  
  .welcome-card .ant-card-body {
    padding: 20px;
  }
  
  .action-card {
    height: 140px;
    margin-bottom: 16px;
  }
  
  .action-content {
    padding: 12px 8px;
  }
  
  .action-icon {
    font-size: 24px;
    margin-bottom: 8px;
  }
  
  .stat-card {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
.dashboard-container .ant-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 卡片悬停效果 */
.stat-card,
.action-card {
  position: relative;
  overflow: hidden;
}

.stat-card::before,
.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.stat-card:hover::before,
.action-card:hover::before {
  left: 100%;
}

/* 统计数字动画 */
.stat-card .ant-statistic-content-value {
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 特性列表样式 */
.feature-item {
  transition: all 0.3s ease;
  border-radius: 4px;
  padding: 8px 12px;
}

.feature-item:hover {
  background: rgba(24, 144, 255, 0.05);
  transform: translateX(4px);
}

/* 活动列表样式 */
.activity-item {
  transition: all 0.3s ease;
  border-radius: 4px;
  padding: 12px;
  margin: 0 -12px;
}

.activity-item:hover {
  background: #fafafa;
  transform: translateX(4px);
}

/* 欢迎卡片渐变动画 */
.welcome-card {
  background-size: 200% 200%;
  animation: gradientShift 6s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
