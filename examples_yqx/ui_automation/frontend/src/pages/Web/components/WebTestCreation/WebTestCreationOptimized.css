/* Web测试创建优化版样式 */
.web-test-creation-optimized {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
}

.web-test-creation-optimized .main-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.web-test-creation-optimized .ant-card-head {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border-bottom: none;
}

.web-test-creation-optimized .ant-card-head-title {
  color: white;
  font-weight: 600;
}

.web-test-creation-optimized .ant-tabs-tab {
  font-weight: 500;
}

.web-test-creation-optimized .ant-tabs-tab-active {
  color: #1890ff;
}

.web-test-creation-optimized .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.web-test-creation-optimized .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  border-radius: 6px;
  font-weight: 500;
}

.web-test-creation-optimized .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.web-test-creation-optimized .ant-upload {
  width: 100%;
}

.web-test-creation-optimized .ant-upload-btn {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.web-test-creation-optimized .ant-upload-btn:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.web-test-creation-optimized .ant-checkbox-group {
  display: flex;
  gap: 16px;
}

.web-test-creation-optimized .ant-checkbox-wrapper {
  font-weight: 500;
}

.web-test-creation-optimized .ant-alert {
  border-radius: 6px;
  border: none;
}

.web-test-creation-optimized .ant-alert-info {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.web-test-creation-optimized .ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

.web-test-creation-optimized .ant-progress {
  margin: 16px 0;
}

.web-test-creation-optimized .ant-card-small .ant-card-head {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.web-test-creation-optimized .ant-card-small .ant-card-head-title {
  color: #262626;
  font-size: 14px;
  font-weight: 600;
}

/* 脚本编辑器样式 */
.web-test-creation-optimized .script-editor {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.web-test-creation-optimized .script-editor .ant-tabs-content-holder {
  padding: 16px;
}

.web-test-creation-optimized .script-editor pre {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  max-height: 500px;
}

.web-test-creation-optimized .script-actions {
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.web-test-creation-optimized .script-actions .ant-btn {
  margin-right: 8px;
}

/* 数据库配置样式 */
.web-test-creation-optimized .database-config {
  background: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
}

.web-test-creation-optimized .database-config .ant-checkbox-wrapper {
  margin-bottom: 12px;
  font-weight: 600;
}

.web-test-creation-optimized .database-config .ant-input {
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .web-test-creation-optimized {
    padding: 8px;
  }
  
  .web-test-creation-optimized .ant-col {
    margin-bottom: 16px;
  }
  
  .web-test-creation-optimized .ant-form-item {
    margin-bottom: 16px;
  }
  
  .web-test-creation-optimized .ant-checkbox-group {
    flex-direction: column;
    gap: 8px;
  }
}

/* 动画效果 */
.web-test-creation-optimized .ant-card {
  transition: all 0.3s ease;
}

.web-test-creation-optimized .ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.web-test-creation-optimized .ant-btn {
  transition: all 0.3s ease;
}

.web-test-creation-optimized .ant-tabs-tab {
  transition: all 0.3s ease;
}

.web-test-creation-optimized .ant-form-item {
  transition: all 0.3s ease;
}

/* 加载状态样式 */
.web-test-creation-optimized .loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.web-test-creation-optimized .loading-content {
  text-align: center;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 成功状态样式 */
.web-test-creation-optimized .success-indicator {
  color: #52c41a;
  font-weight: 600;
}

.web-test-creation-optimized .error-indicator {
  color: #ff4d4f;
  font-weight: 600;
}

.web-test-creation-optimized .warning-indicator {
  color: #faad14;
  font-weight: 600;
}

/* 紧凑布局样式 */
.web-test-creation-optimized .compact-form .ant-form-item {
  margin-bottom: 12px;
}

.web-test-creation-optimized .compact-form .ant-form-item-label {
  padding-bottom: 4px;
}

.web-test-creation-optimized .inline-form .ant-form-item {
  display: inline-block;
  margin-right: 16px;
  margin-bottom: 8px;
}

/* 脚本统计样式 */
.web-test-creation-optimized .script-stats {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f7ff 100%);
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
}

.web-test-creation-optimized .script-stats .ant-statistic {
  text-align: center;
}

.web-test-creation-optimized .script-stats .ant-statistic-content {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.web-test-creation-optimized .script-stats .ant-statistic-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}
