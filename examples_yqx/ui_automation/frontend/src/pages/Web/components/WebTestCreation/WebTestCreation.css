/* Web测试创建组件 V2 样式 */
.web-test-creation-v2 {
  padding: 16px;
  background: #f8fafc;
  min-height: 100vh;
}

.web-test-creation-v2 .ant-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  background: white;
}

/* 文本输入区域样式 */
.text-input-section {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
}

.text-input-section .w-md-editor {
  border-radius: 8px;
  overflow: hidden;
}

.text-input-section .w-md-editor-text-container {
  font-size: 14px;
  line-height: 1.6;
}

.text-input-section .w-md-editor-text {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* 图片输入区域样式 */
.image-input-section {
  background: white;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  margin-bottom: 12px;
}

.image-input-section .ant-upload-drag {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: #f9fafb;
  transition: all 0.3s ease;
  min-height: 100px;
}

.image-input-section .ant-upload-drag:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.image-input-section .ant-upload-drag.ant-upload-drag-hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

/* 配置区域样式 */
.web-test-creation-v2 .ant-form-item {
  margin-bottom: 12px;
}

.web-test-creation-v2 .ant-form-item-label > label {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
}

.web-test-creation-v2 .ant-form-item-label {
  padding-bottom: 4px;
}

/* 生成按钮样式 */
.web-test-creation-v2 .ant-btn-primary.ant-btn-lg {
  height: 52px;
  padding: 0 36px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: none;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.web-test-creation-v2 .ant-btn-primary.ant-btn-lg:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

/* 脚本展示区域样式 */
.web-test-creation-v2 .script-display {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.web-test-creation-v2 .script-display .ant-tabs-content-holder {
  padding: 16px;
}

.web-test-creation-v2 .script-display pre {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 16px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
  overflow-x: auto;
  margin: 0;
}

/* 快速模板按钮样式 */
.web-test-creation-v2 .template-buttons {
  margin-top: 12px;
}

.web-test-creation-v2 .template-buttons .ant-btn {
  margin-right: 8px;
  margin-bottom: 8px;
  border-radius: 6px;
  font-size: 12px;
}

/* 标签页样式优化 */
.web-test-creation-v2 .ant-tabs-tab {
  padding: 12px 16px;
  font-weight: 500;
}

.web-test-creation-v2 .ant-tabs-tab-active {
  color: #1890ff;
}

.web-test-creation-v2 .ant-tabs-ink-bar {
  background: #1890ff;
  height: 3px;
  border-radius: 2px;
}

/* 流式显示区域样式 */
.web-test-creation-v2 .streaming-display {
  background: white;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  max-height: 400px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .web-test-creation-v2 {
    padding: 16px;
  }
  
  .web-test-creation-v2 .ant-col {
    margin-bottom: 24px;
  }
}

@media (max-width: 768px) {
  .web-test-creation-v2 {
    padding: 12px;
  }
  
  .text-input-section,
  .image-input-section {
    padding: 12px;
  }
  
  .web-test-creation-v2 .w-md-editor {
    height: 300px !important;
  }
}

/* 加载状态样式 */
.web-test-creation-v2 .loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  z-index: 10;
}

/* 成功状态样式 */
.web-test-creation-v2 .success-indicator {
  color: #52c41a;
  font-weight: 600;
}

/* 错误状态样式 */
.web-test-creation-v2 .error-indicator {
  color: #ff4d4f;
  font-weight: 600;
}

/* 警告状态样式 */
.web-test-creation-v2 .warning-indicator {
  color: #faad14;
  font-weight: 600;
}

/* 动画效果 */
.web-test-creation-v2 .fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 脚本操作按钮样式 */
.web-test-creation-v2 .script-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.web-test-creation-v2 .script-actions .ant-btn {
  border-radius: 6px;
  font-size: 12px;
  height: 28px;
  padding: 0 12px;
}

/* 图片预览样式 */
.web-test-creation-v2 .image-preview {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.web-test-creation-v2 .image-preview img {
  width: 100%;
  height: auto;
  display: block;
}

/* 提示信息样式 */
.web-test-creation-v2 .hint-text {
  color: #8c8c8c;
  font-size: 12px;
  margin-top: 4px;
}

/* 分割线样式 */
.web-test-creation-v2 .ant-divider {
  margin: 24px 0;
  border-color: #f0f0f0;
}

/* 卡片标题样式 */
.web-test-creation-v2 .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

/* 选择器样式 */
.web-test-creation-v2 .ant-select-multiple .ant-select-selection-item {
  background: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  color: #24292e;
}

/* 文本域样式 */
.web-test-creation-v2 .ant-input {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.web-test-creation-v2 .ant-input:focus,
.web-test-creation-v2 .ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 标签样式 */
.web-test-creation-v2 .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 分析日志容器滚动条样式 */
.analysis-log-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f8f9fa;
  /* 强制显示滚动条 */
  overflow-y: scroll !important;
}

/* Webkit浏览器滚动条样式 */
.analysis-log-container::-webkit-scrollbar {
  width: 8px;
  display: block;
}

.analysis-log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.analysis-log-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.analysis-log-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 确保滚动条在内容溢出时显示 */
.analysis-log-container {
  overflow-y: scroll !important;
  overflow-x: hidden !important;
  /* 确保容器有明确的高度限制 */
  box-sizing: border-box;
  /* 确保滚动条始终可见 */
  scrollbar-gutter: stable;
}

/* 右侧面板优化 */
.web-test-creation-v2 .ant-col {
  display: flex;
  flex-direction: column;
}

.web-test-creation-v2 .ant-card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px !important;
}

/* 空状态样式优化 */
.web-test-creation-v2 .empty-state {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 标题区域优化 */
.web-test-creation-v2 .ant-card-head {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  border-radius: 16px 16px 0 0;
}

.web-test-creation-v2 .ant-card-head-title {
  font-weight: 600;
  color: #1e293b;
}

/* 进度条优化 */
.web-test-creation-v2 .ant-progress-bg {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
}

/* 标签优化 */
.web-test-creation-v2 .ant-tag {
  border-radius: 8px;
  font-weight: 500;
}

/* 快速模板按钮优化 */
.web-test-creation-v2 .ant-btn-dashed {
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.web-test-creation-v2 .ant-btn-dashed:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
  color: #3b82f6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .web-test-creation-v2 {
    padding: 16px;
  }

  .web-test-creation-v2 .ant-row {
    flex-direction: column;
  }

  .web-test-creation-v2 .ant-col {
    width: 100% !important;
    max-width: 100% !important;
  }
}

/* 动画效果 */
.web-test-creation-v2 .ant-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
.web-test-creation-v2 .ant-btn-loading {
  position: relative;
  overflow: hidden;
}

.web-test-creation-v2 .ant-btn-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}
