/* 设置页面样式 */
.settings-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.page-header {
  text-align: center;
  margin-bottom: 32px;
  padding: 32px 0;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-icon {
  color: #722ed1;
  margin-right: 12px;
  font-size: 28px;
}

.settings-card {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-card .ant-card-body {
  padding: 0;
}

.settings-tabs {
  min-height: 600px;
}

.settings-tabs .ant-tabs-tab {
  padding: 16px 24px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px 0 0 8px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.settings-tabs .ant-tabs-tab:hover {
  background: rgba(114, 46, 209, 0.05);
}

.settings-tabs .ant-tabs-tab-active {
  background: linear-gradient(135deg, #722ed1, #9254de);
  color: white;
}

.settings-tabs .ant-tabs-tab-active .anticon {
  color: white;
}

.settings-tabs .ant-tabs-content {
  padding: 32px;
  background: white;
  border-radius: 0 12px 12px 0;
}

.settings-tabs .ant-tabs-tabpane {
  min-height: 500px;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input,
.ant-input-number,
.ant-select-selector {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.ant-input:focus,
.ant-input-number:focus,
.ant-select-focused .ant-select-selector {
  border-color: #722ed1;
  box-shadow: 0 0 0 2px rgba(114, 46, 209, 0.2);
}

/* 开关样式 */
.ant-switch-checked {
  background-color: #722ed1;
}

/* 分割线样式 */
.ant-divider {
  border-color: #f0f0f0;
  margin: 24px 0;
}

/* 标题样式 */
.ant-typography h4 {
  color: #262626;
  font-weight: 600;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

/* 描述文字样式 */
.ant-typography-caption {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  display: block;
}

/* 警告框样式 */
.ant-alert {
  border-radius: 8px;
  border: none;
}

.ant-alert-info {
  background: linear-gradient(135deg, #e6f7ff, #f0f9ff);
  border-left: 4px solid #1890ff;
}

.ant-alert-warning {
  background: linear-gradient(135deg, #fffbe6, #fff7e6);
  border-left: 4px solid #faad14;
}

/* 操作按钮区域 */
.settings-actions {
  text-align: center;
  padding: 24px 32px;
  background: #fafafa;
  border-radius: 0 0 12px 12px;
}

.settings-actions .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  min-width: 120px;
}

.settings-actions .ant-btn-primary {
  background: linear-gradient(135deg, #722ed1, #9254de);
  border: none;
  transition: all 0.3s ease;
}

.settings-actions .ant-btn-primary:hover {
  background: linear-gradient(135deg, #9254de, #b37feb);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .settings-container {
    padding: 16px;
  }
  
  .settings-tabs .ant-tabs-content {
    padding: 24px;
  }
}

@media (max-width: 992px) {
  .settings-tabs {
    min-height: auto;
  }
  
  .settings-tabs .ant-tabs-content {
    padding: 20px;
  }
  
  .settings-actions {
    padding: 20px 24px;
  }
}

@media (max-width: 768px) {
  .settings-container {
    padding: 12px;
  }
  
  .page-header {
    padding: 24px 16px;
    margin-bottom: 24px;
  }
  
  .settings-tabs {
    flex-direction: column;
  }
  
  .settings-tabs .ant-tabs-tab {
    border-radius: 8px;
    margin-bottom: 4px;
  }
  
  .settings-tabs .ant-tabs-content {
    padding: 16px;
    border-radius: 0 0 12px 12px;
  }
  
  .settings-actions {
    padding: 16px;
  }
  
  .settings-actions .ant-space {
    width: 100%;
    justify-content: center;
  }
  
  .settings-actions .ant-btn {
    width: 100%;
    margin-bottom: 8px;
  }
}

/* 动画效果 */
.settings-container .ant-card {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 表单项悬停效果 */
.ant-form-item {
  transition: all 0.3s ease;
  border-radius: 6px;
  padding: 8px;
  margin: 0 -8px 24px -8px;
}

.ant-form-item:hover {
  background: rgba(114, 46, 209, 0.02);
}

/* 开关悬停效果 */
.ant-switch {
  transition: all 0.3s ease;
}

.ant-switch:hover {
  transform: scale(1.05);
}

/* 输入框聚焦动画 */
.ant-input:focus,
.ant-input-number:focus {
  animation: inputFocus 0.3s ease-out;
}

@keyframes inputFocus {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* 选择器样式 */
.ant-select-dropdown {
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.ant-select-item-option-selected {
  background: rgba(114, 46, 209, 0.1);
  color: #722ed1;
}

/* 数字输入框样式 */
.ant-input-number {
  width: 100%;
}

.ant-input-number-handler-wrap {
  border-radius: 0 6px 6px 0;
}

/* 密码输入框样式 */
.ant-input-password {
  border-radius: 6px;
}

.ant-input-password .ant-input {
  border-radius: 6px 0 0 6px;
}

.ant-input-password .ant-input-suffix {
  border-radius: 0 6px 6px 0;
}

/* 标签页内容滚动 */
.settings-tabs .ant-tabs-tabpane {
  overflow-y: auto;
  max-height: 70vh;
}

.settings-tabs .ant-tabs-tabpane::-webkit-scrollbar {
  width: 6px;
}

.settings-tabs .ant-tabs-tabpane::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.settings-tabs .ant-tabs-tabpane::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.settings-tabs .ant-tabs-tabpane::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 保存按钮加载状态 */
.ant-btn-loading {
  pointer-events: none;
}

.ant-btn-loading .ant-btn-loading-icon {
  color: white;
}

/* 重置按钮样式 */
.settings-actions .ant-btn:not(.ant-btn-primary) {
  border-color: #d9d9d9;
  color: #595959;
}

.settings-actions .ant-btn:not(.ant-btn-primary):hover {
  border-color: #722ed1;
  color: #722ed1;
  transform: translateY(-1px);
}
