# UI自动化测试系统 - 环境变量配置模板
# 复制此文件为 .env 并填入实际配置值

# ==================== 应用基础配置 ====================
APP_NAME=UI自动化测试系统
APP_VERSION=2.0.0
DEBUG=false
API_V1_STR=/api/v1

# ==================== 服务器配置 ====================
HOST=0.0.0.0
PORT=8000
RELOAD=true

# ==================== 安全配置 ====================
SECRET_KEY=your-secret-key-here-please-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=11520
ALGORITHM=HS256

# ==================== CORS配置 ====================
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://127.0.0.1:3000,http://127.0.0.1:3001

# ==================== 数据库配置 ====================
# MySQL配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=automation_db

# Neo4j配置
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=ui_automation_vectors

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=your_redis_password

# ==================== AI模型配置 ====================
# DeepSeek配置
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# Qwen-VL配置
QWEN_VL_API_KEY=your_qwen_vl_api_key
QWEN_VL_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
QWEN_VL_MODEL=qwen-vl-max-latest

# UI-TARS配置
UI_TARS_API_KEY=your_ui_tars_api_key
UI_TARS_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
UI_TARS_MODEL=doubao-1-5-ui-tars-250428

# OpenAI配置（可选）
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# ==================== 文件存储配置 ====================
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600
ALLOWED_EXTENSIONS=.pdf,.doc,.docx,.txt,.md,.yaml,.yml
ALLOWED_IMAGE_EXTENSIONS=.png,.jpg,.jpeg,.gif,.bmp,.webp
IMAGE_UPLOAD_DIR=uploads/images
YAML_OUTPUT_DIR=uploads/yaml
PLAYWRIGHT_OUTPUT_DIR=uploads/playwright
MAX_IMAGE_SIZE=10485760

# ==================== 自动化工具配置 ====================
# MidScene.js配置
MIDSCENE_SERVICE_URL=http://localhost:3002
MIDSCENE_TIMEOUT=300
MIDSCENE_SCRIPT_PATH=C:\Users\<USER>\Desktop\workspace\playwright-workspace

# Playwright配置
PLAYWRIGHT_HEADLESS=true
PLAYWRIGHT_TIMEOUT=30000
PLAYWRIGHT_VIEWPORT_WIDTH=1280
PLAYWRIGHT_VIEWPORT_HEIGHT=960

# AutoGen配置
AUTOGEN_CACHE_ENABLED=true
AUTOGEN_MAX_ROUND=10
AUTOGEN_TIMEOUT=600

# ==================== 日志配置 ====================
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# ==================== 监控配置 ====================
ENABLE_METRICS=true
METRICS_PORT=8001
ENABLE_MONITORING=true

# ==================== 功能开关 ====================
ENABLE_CACHING=true
ENABLE_RATE_LIMITING=true
ENABLE_ASYNC_PROCESSING=true
HYBRID_RETRIEVAL_ENABLED=true
VECTOR_SEARCH_TOP_K=10
SIMILARITY_THRESHOLD=0.7
