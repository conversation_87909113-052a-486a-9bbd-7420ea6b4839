"""
UI自动化测试系统 - 枚举类型定义
统一管理所有枚举类型，避免重复定义
enums.py模块-> 给程序逻辑用的
enums.py中的枚举类权威的定义了系统中到底存在哪些类型的智能体，是整个系统的名录，唯一标识符
通过强制 factory.py 和 constants.py 都从 enums.py 获取这个标识符，确保整个系统的一致性和健壮性

"""
from enum import Enum


class AgentPlatform(Enum):
    """智能体平台类型"""
    WEB = "web"
    ANDROID = "android"
    API = "api"
    COMMON = "common"

# AgentTypes 定义了一组规范化的、程序内部使用的标识符， 当写AgentTypes.IMAGE_ANALYZER时，得到的不是一个普通字符串，而是一个枚举成员
# 值（.value）: 如"image_analyzer" 被用作机器可读的、稳定的键（Key）。如在factory.py中，它被用作_agent_classes字典的键,这是非常核心的程序逻辑

class AgentTypes(Enum):
    # 图片分析智能体
    UI_EXPERT = "ui_expert"
    INTERACTION_ANALYST = "interaction_analyst"
    QUALITY_REVIEWER = "quality_reviewer"
    MIDSCENE_EXPERT = "midscene_expert"

    """智能体类型枚举"""
    # 分析类智能体
    MULTIMODAL_ANALYZER = "multimodal_analyzer"      # 多模态分析智能体（图片/URL分析）
    WEB_SCRAPER = "web_scraper"                      # 网页抓取智能体（Crawl4AI）
    API_ANALYZER = "api_analyzer"                    # API接口分析智能体
    IMAGE_ANALYZER = "image_analyzer"                # 图片分析智能体
    PAGE_ANALYZER = "page_analyzer"
    IMAGE_DESCRIPTION_GENERATOR = "image_description_generator"

    # 生成类智能体
    TEST_PLANNER = "test_planner"                    # 测试规划智能体
    YAML_GENERATOR = "yaml_generator"                # YAML生成智能体（MidScene.js）
    PLAYWRIGHT_GENERATOR = "playwright_generator"    # Playwright代码生成智能体
    API_TEST_GENERATOR = "api_test_generator"        # API测试生成智能体
    ACTION_GENERATOR = "action_generator"            # 动作生成智能体（保持兼容性）

    # 执行类智能体
    YAML_EXECUTOR = "yaml_executor"                  # YAML脚本执行智能体
    PLAYWRIGHT_EXECUTOR = "playwright_executor"      # Playwright执行智能体
    API_TEST_EXECUTOR = "api_test_executor"          # API测试执行智能体

    # 管理类智能体
    RESULT_ANALYZER = "result_analyzer"              # 结果分析智能体
    REPORT_GENERATOR = "report_generator"            # 报告生成智能体
    SCRIPT_MANAGER = "script_manager"                # 脚本管理智能体
    SCRIPT_DATABASE_SAVER = "script_database_saver"  # 脚本数据库保存智能体
    PAGE_ANALYSIS_STORAGE = "page_analysis_storage"  # 页面分析存储智能体

    # 解析类智能体
    TEST_CASE_ELEMENT_PARSER = "test_case_element_parser"  # 测试用例元素解析智能体


class TopicTypes(Enum):
    """主题类型枚举"""
    # 分析类主题
    MULTIMODAL_ANALYZER = "multimodal_analyzer"
    WEB_SCRAPER = "web_scraper"
    API_ANALYZER = "api_analyzer"

    # 生成类主题
    TEST_PLANNER = "test_planner"
    YAML_GENERATOR = "yaml_generator"
    PLAYWRIGHT_GENERATOR = "playwright_generator"
    API_TEST_GENERATOR = "api_test_generator"
    ACTION_GENERATOR = "action_generator"            # 动作生成主题（保持兼容性）
    IMAGE_ANALYZER = "image_analyzer"                # Web分析主题
    PAGE_ANALYZER = "page_analyzer"  # 页面分析主题
    IMAGE_DESCRIPTION_GENERATOR="image_description_generator"

    # 执行类主题
    YAML_EXECUTOR = "yaml_executor"
    PLAYWRIGHT_EXECUTOR = "playwright_executor"
    API_TEST_EXECUTOR = "api_test_executor"

    # 管理类主题
    RESULT_ANALYZER = "result_analyzer"
    REPORT_GENERATOR = "report_generator"
    SCRIPT_MANAGER = "script_manager"
    SCRIPT_DATABASE_SAVER = "script_database_saver"
    PAGE_ANALYSIS_STORAGE = "page_analysis_storage"

    # 解析类主题
    TEST_CASE_ELEMENT_PARSER = "test_case_element_parser"

    # 系统主题
    STREAM_OUTPUT = "stream_output"


class TestTypes(Enum):
    """测试类型枚举"""
    FUNCTIONAL = "functional"                        # 功能测试
    UI_INTERACTION = "ui_interaction"                # UI交互测试
    REGRESSION = "regression"                        # 回归测试
    SMOKE = "smoke"                                  # 冒烟测试
    INTEGRATION = "integration"                      # 集成测试
    PERFORMANCE = "performance"                      # 性能测试
    ACCESSIBILITY = "accessibility"                  # 可访问性测试
    CROSS_BROWSER = "cross_browser"                  # 跨浏览器测试
    MOBILE_RESPONSIVE = "mobile_responsive"          # 移动端响应式测试
    API_INTEGRATION = "api_integration"              # API集成测试


class ActionTypes(Enum):
    """动作类型枚举"""
    CLICK = "click"                                  # 点击
    INPUT = "input"                                  # 输入
    HOVER = "hover"                                  # 悬停
    SCROLL = "scroll"                                # 滚动
    WAIT = "wait"                                    # 等待
    ASSERT = "assert"                                # 断言
    NAVIGATE = "navigate"                            # 导航
    SCREENSHOT = "screenshot"                        # 截图
    KEYBOARD_PRESS = "keyboard_press"                # 按键
    DRAG_DROP = "drag_drop"                          # 拖拽
    SELECT = "select"                                # 选择
    UPLOAD = "upload"                                # 上传文件


class BrowserTypes(Enum):
    """浏览器类型枚举"""
    CHROMIUM = "chromium"
    FIREFOX = "firefox"
    WEBKIT = "webkit"
    CHROME = "chrome"
    EDGE = "edge"


class DeviceTypes(Enum):
    """设备类型枚举"""
    DESKTOP = "desktop"
    TABLET = "tablet"
    MOBILE = "mobile"
    CUSTOM = "custom"


class MessageRegion(Enum):
    """消息区域类型"""
    PROCESS = "process"
    ANALYSIS = "analysis"
    GENERATION = "generation"
    REVIEW = "review"
    TESTCASE = "testcase"  # 最终测试用例区域
    ERROR = "error"
    SUCCESS = "success"
    WARNING = "warning"
    INFO = "info"


class SessionStatus(Enum):
    """会话状态枚举"""
    ACTIVE = "active"
    CLOSED = "closed"
    EXPIRED = "expired"
    SUSPENDED = "suspended"
