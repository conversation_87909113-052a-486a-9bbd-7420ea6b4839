"""
UI自动化测试系统 - 类型定义模块
统一管理所有枚举、数据模型和常量定义
"""

# 导出所有类型定义
from .enums import *
from .models import *
from .constants import *


# __all__是一个定义在模块顶层的字符串列表，列表中的每个字符串都是一个名字，代表希望从这个模块中导出的类、函数或变量，定义了包的公共API
# 如果一个模块定义了__all__ ,那么from <module> import * 只会导入__all__列表中列出的那些名字,精确控制 from <module> import * 的行为，防止命名空间污染。
# 如果一个模块没有定义 __all__：那么 from <module> import * 就会回到默认行为，导入所有不以下划线开头的名字。

__all__ = [
    # 枚举类型
    'AgentPlatform', 'AgentTypes', 'TopicTypes', 'TestTypes', 
    'ActionTypes', 'BrowserTypes', 'DeviceTypes', 'MessageRegion',
    
    # 数据模型
    'TestCase', 'TestAction', 'TestResult', 'UIElement', 'TestEnvironment', 'TestExecutionContext',
    
    # 常量和映射
    'AGENT_NAMES', 'TOPIC_TYPES', 'DEFAULT_TEST_ENVIRONMENT', 'DEVICE_PRESETS'
]
