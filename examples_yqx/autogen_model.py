import asyncio
import os

from autogen_ext.models.openai import OpenAIChatCompletionClient
from autogen_core.models import UserMessage, SystemMessage, ModelFamily

# 设置代理（如果需要的话）
# 如果你在公司网络或需要代理，请取消注释并配置以下代理设置
# os.environ["HTTP_PROXY"] = "http://your-proxy-server:port"
# os.environ["HTTPS_PROXY"] = "http://your-proxy-server:port"

# 或者如果你不需要代理，可以显式禁用
# os.environ["HTTP_PROXY"] = ""
# os.environ["HTTPS_PROXY"] = ""

# OpenAIChatCompletionClient是由AutoGen项目扩展出来的一个封装类
# 后面用这个类创建的对象赋给Agent
openai_model_client = OpenAIChatCompletionClient(
    model="deepseek-chat",
    api_key="***********************************",
    base_url="https://api.deepseek.com/v1",
    # 如果没有model_info字段，运行代码报错，根据报错信息增加下面字段
    model_info={
        "vision": False,
        "function_calling": True,
        "json_output": True,
        "family": ModelFamily.UNKNOWN,
        "structured_output": True,
        "multiple_system_messages": True,
    }
)

# 由于业务复杂，需要调用工具，实际开发过程中不会用下面的方式调用，需要将上面的LLM 调用封装成一个模块。
# 用Autogen AgentChat的Agents来调用它
# LlamaIndex Langchain 也都是用自己封装的组件与LLM交互，不用OpenAI官方SDK(openai)
# 定义一个协程函数
async def main():
    try:
        print("正在连接到DeepSeek API...")
        result = await openai_model_client.create([
            UserMessage(content="编写一段冒泡排序的代码", source="user"),
            SystemMessage(content="你是Python编程专家")
        ])
        print("API调用成功！")
        print(result)
    except Exception as e:
        print(f"连接失败: {e}")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 检查API密钥是否正确")
        print("3. 如果在公司网络，可能需要配置代理设置")
        print("4. 检查防火墙设置")
        print("5. 尝试使用VPN或更换网络环境")
    finally:
        await openai_model_client.close()

if __name__ == "__main__":
    asyncio.run(main())
