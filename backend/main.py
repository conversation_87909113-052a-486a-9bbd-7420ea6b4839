import os
import io
import json
import pandas as pd
import requests
from typing import List
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

app = FastAPI(title="FMEA信号接口智能映射工具", version="1.0.0")

# 添加CORS中间件
allowed_origins = os.getenv("ALLOWED_ORIGINS", "*")
# 如果是逗号分隔的多个域名，转换为列表
if allowed_origins == "*":
    origins = ["*"]
else:
    origins = [origin.strip() for origin in allowed_origins.split(",")]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# LLM配置
def get_llm_config():
    """获取LLM配置"""
    # 从环境变量获取配置，提供默认值作为备用
    model = os.getenv("MODEL", "deepseek-chat")
    api_key = os.getenv("API_KEY")
    base_url = os.getenv("BASE_URL", "https://api.deepseek.com/v1")
    timeout = float(os.getenv("REQUEST_TIMEOUT", "60.0"))
    max_retries = int(os.getenv("MAX_RETRIES", "3"))

    # 检查必需的环境变量
    if not api_key:
        raise ValueError("API_KEY环境变量未设置，请检查.env文件")

    return {
        "model": model,
        "api_key": api_key,
        "base_url": base_url,
        "timeout": timeout,
        "max_retries": max_retries
    }

def construct_llm_prompt(platform_signals, customer_signals):
    """构建LLM提示词"""
    platform_signals_str = "\\n".join([f"- {s}" for s in platform_signals])
    customer_signals_str = "\\n".join([f"- {s}" for s in customer_signals])
    prompt_template = f"""
    ### 角色 ###
    你是一位资深的汽车电子工程师，精通整车网络通信（特别是CAN总线）、功能安全和FMEA（失效模式与影响分析）。

    ### 任务 ###
    你的任务是精确地将一个通用的"平台信号列表"中的每一个信号，映射到"客户信号列表"中功能最匹配的信号。

    ### 分析要求 ###
    1.  **完整性**: 必须对"平台信号列表"中的 **每一个** 信号进行分析和映射。
    2.  **精确性**: 深入理解每个信号的专业语义。例如，要严格区分'实际扭矩(Actual Torque)'和'请求扭矩(Request Torque)'。
    3.  **专业推理**: 你的每一个映射决策都必须附带一个详细、专业且令人信服的理由（'reason'）。
    4.  **处理无法匹配的情况**: 如果平台信号在客户列表中找不到合理的匹配项，请将'customer_signal'字段的值设为 "No match found"，并在理由中清晰地解释无法匹配的原因。

    ### 输入数据 ###
    **平台信号列表**:
    ```
    {platform_signals_str}
    ```
    **客户信号列表**:
    ```
    {customer_signals_str}
    ```
    ### 输出格式 ###
    请 **严格** 按照以下JSON格式返回你的分析结果，不要添加任何额外的解释或说明文字。
    ```json
    [
        {{"platform_signal": "...", "customer_signal": "...", "reason": "..."}},
        ...
    ]
    ```
    """
    return prompt_template.strip()

async def call_llm_for_mapping(prompt):
    """调用LLM进行信号映射"""
    try:
        # 获取LLM配置
        config = get_llm_config()

        # 构建请求数据
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": config["model"],
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 4000
        }

        # 发送请求（禁用代理以避免网络问题）
        response = requests.post(
            f"{config['base_url']}/chat/completions",
            headers=headers,
            json=data,
            timeout=config["timeout"],
            proxies={"http": None, "https": None}  # 禁用代理
        )

        if response.status_code == 200:
            result = response.json()
            if result.get("choices") and len(result["choices"]) > 0:
                return result["choices"][0]["message"]["content"]
            else:
                raise HTTPException(status_code=500, detail="LLM返回空响应")
        else:
            print(f"LLM API错误: {response.status_code} - {response.text}")
            raise HTTPException(status_code=500, detail=f"LLM API调用失败: {response.status_code}")

    except requests.exceptions.RequestException as e:
        print(f"LLM请求错误: {str(e)}")
        # 返回模拟数据作为备用
        mock_response_json = """
        [
            {"platform_signal": "示例平台信号", "customer_signal": "示例客户信号", "reason": "这是一个模拟映射结果，因为LLM调用失败"}
        ]
        """
        return mock_response_json
    except Exception as e:
        print(f"LLM调用错误: {str(e)}")
        # 返回模拟数据作为备用
        mock_response_json = """
        [
            {"platform_signal": "示例平台信号", "customer_signal": "示例客户信号", "reason": "这是一个模拟映射结果，因为LLM调用失败"}
        ]
        """
        return mock_response_json

def validate_file(filename: str, file_size: int) -> None:
    """验证上传的文件"""
    # 检查文件扩展名
    allowed_extensions = os.getenv("ALLOWED_EXTENSIONS", ".txt,.csv").split(",")
    file_ext = os.path.splitext(filename)[1].lower()

    if file_ext not in allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"不支持的文件格式 {file_ext}，支持的格式: {', '.join(allowed_extensions)}"
        )

    # 检查文件大小
    max_size_mb = int(os.getenv("MAX_FILE_SIZE", "10"))
    max_size_bytes = max_size_mb * 1024 * 1024

    if file_size > max_size_bytes:
        raise HTTPException(
            status_code=400,
            detail=f"文件大小超过限制 {max_size_mb}MB"
        )

def read_file_content(file_content: bytes, filename: str) -> List[str]:
    """读取文件内容并提取信号列表"""
    file_ext = os.path.splitext(filename)[1].lower()

    if file_ext in ['.xlsx', '.xls']:
        # 处理Excel文件
        try:
            print(f"🔍 正在解析Excel文件: {filename}")
            # 使用pandas读取Excel文件
            df = pd.read_excel(io.BytesIO(file_content), header=None)

            print(f"📊 Excel文件信息:")
            print(f"   行数: {len(df)}")
            print(f"   列数: {len(df.columns) if not df.empty else 0}")

            # 获取数据作为信号列表
            if not df.empty and len(df.columns) > 0:
                signals = []

                # 检查所有列，找到包含数据的列
                for col_idx in range(len(df.columns)):
                    column = df.iloc[:, col_idx]
                    column_no_na = column.dropna()
                    print(f"   第{col_idx+1}列: 总行数{len(column)}, 非空行数{len(column_no_na)}")

                    if len(column_no_na) > 0:
                        print(f"   第{col_idx+1}列前3行数据: {column_no_na.head(3).tolist()}")

                        # 如果这一列有数据，提取信号
                        for i, value in enumerate(column):
                            if pd.isna(value):
                                continue

                            str_value = str(value).strip()
                            if str_value and str_value.lower() not in ['nan', 'none', '']:
                                signals.append(str_value)

                        # 找到第一个有数据的列就停止
                        if len(signals) > 0:
                            print(f"   使用第{col_idx+1}列数据")
                            break

                print(f"   提取的信号数量: {len(signals)}")
                if len(signals) > 0:
                    print(f"   前3个信号示例: {signals[:3]}")

                return signals
            else:
                raise HTTPException(status_code=400, detail="Excel文件为空或没有数据")

        except Exception as e:
            print(f"❌ Excel文件读取失败: {str(e)}")
            import traceback
            print(f"❌ 详细错误: {traceback.format_exc()}")
            raise HTTPException(status_code=400, detail=f"Excel文件读取失败: {str(e)}")

    else:
        # 处理文本文件 (.txt, .csv)
        try:
            # 尝试以UTF-8编码读取
            content = file_content.decode('utf-8')
        except UnicodeDecodeError:
            try:
                # 如果UTF-8失败，尝试GBK编码
                content = file_content.decode('gbk')
            except UnicodeDecodeError:
                # 如果都失败，尝试latin-1编码
                content = file_content.decode('latin-1')

        # 按行分割并过滤空行
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        return lines

@app.get("/")
async def root():
    """根路径"""
    return {"message": "FMEA信号接口智能映射工具API"}

@app.post("/api/map_signals")
async def map_signals(
    platform_file: UploadFile = File(..., description="平台接口文件"),
    customer_file: UploadFile = File(..., description="客户接口文件")
):
    """信号映射API接口"""
    try:
        print(f"📁 接收到文件上传请求:")
        print(f"   平台文件: {platform_file.filename}")
        print(f"   客户文件: {customer_file.filename}")

        # 读取上传的文件
        platform_content = await platform_file.read()
        customer_content = await customer_file.read()

        print(f"📊 文件大小:")
        print(f"   平台文件: {len(platform_content)} bytes")
        print(f"   客户文件: {len(customer_content)} bytes")

        # 验证上传的文件（使用实际文件大小）
        validate_file(platform_file.filename, len(platform_content))
        validate_file(customer_file.filename, len(customer_content))

        print("✅ 文件验证通过")
        
        # 提取信号列表
        print("🔍 开始解析文件内容...")
        platform_signals = read_file_content(platform_content, platform_file.filename)
        customer_signals = read_file_content(customer_content, customer_file.filename)

        print(f"📋 解析结果:")
        print(f"   平台信号数量: {len(platform_signals)}")
        print(f"   客户信号数量: {len(customer_signals)}")

        if len(platform_signals) == 0:
            raise HTTPException(status_code=400, detail="平台文件中未找到有效信号")
        if len(customer_signals) == 0:
            raise HTTPException(status_code=400, detail="客户文件中未找到有效信号")
        
        if not platform_signals:
            raise HTTPException(status_code=400, detail="平台信号文件为空或格式不正确")
        if not customer_signals:
            raise HTTPException(status_code=400, detail="客户信号文件为空或格式不正确")
        
        # 构建LLM提示词
        prompt = construct_llm_prompt(platform_signals, customer_signals)
        
        # 调用LLM进行映射
        llm_response = await call_llm_for_mapping(prompt)
        
        # 解析LLM返回的JSON
        try:
            mapping_results = json.loads(llm_response)
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试提取JSON部分
            import re
            json_match = re.search(r'\[.*\]', llm_response, re.DOTALL)
            if json_match:
                mapping_results = json.loads(json_match.group())
            else:
                raise HTTPException(status_code=500, detail="LLM返回的数据格式不正确")
        
        # 创建DataFrame
        df = pd.DataFrame(mapping_results)
        
        # 创建Excel文件在内存中
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='信号映射结果', index=False)
        
        excel_buffer.seek(0)
        
        # 返回Excel文件
        return StreamingResponse(
            io.BytesIO(excel_buffer.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": "attachment; filename=fmea_mapping_result.xlsx"}
        )
        
    except HTTPException as e:
        print(f"❌ HTTP异常: {e.detail}")
        raise
    except Exception as e:
        print(f"❌ 处理错误: {str(e)}")
        print(f"❌ 错误类型: {type(e).__name__}")
        import traceback
        print(f"❌ 错误堆栈: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")

if __name__ == "__main__":
    import uvicorn

    # 从环境变量获取服务器配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    log_level = os.getenv("LOG_LEVEL", "info").lower()

    print(f"🚀 启动FMEA信号接口智能映射工具后端服务")
    print(f"📡 服务地址: http://{host}:{port}")
    print(f"📚 API文档: http://{host}:{port}/docs")
    print(f"🔧 使用模型: {os.getenv('MODEL', 'deepseek-chat')}")
    print(f"📝 日志级别: {log_level}")

    uvicorn.run(
        app,
        host=host,
        port=port,
        log_level=log_level,
        reload=False  # 生产环境建议设为False
    )
