#!/usr/bin/env python3
"""
FMEA信号接口智能映射工具 - 配置检查脚本
用于验证环境变量配置是否正确
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv

def check_env_file():
    """检查.env文件是否存在"""
    env_file = Path(__file__).parent / ".env"
    if not env_file.exists():
        print("❌ .env文件不存在")
        print("💡 请复制 .env.example 为 .env 并配置相应的值")
        return False
    
    print("✅ .env文件存在")
    return True

def check_required_vars():
    """检查必需的环境变量"""
    required_vars = {
        "MODEL": "LLM模型名称",
        "API_KEY": "API密钥",
        "BASE_URL": "API基础URL",
        "HOST": "服务器主机地址",
        "PORT": "服务器端口"
    }
    
    missing_vars = []
    invalid_vars = []
    
    for var, description in required_vars.items():
        value = os.getenv(var)
        if not value:
            missing_vars.append(f"{var} ({description})")
        elif var == "API_KEY" and value == "your_api_key_here":
            invalid_vars.append(f"{var} - 仍使用默认值，请设置实际的API密钥")
        elif var == "PORT":
            try:
                int(value)
            except ValueError:
                invalid_vars.append(f"{var} - 端口号必须是数字")
    
    if missing_vars:
        print("❌ 缺少必需的环境变量:")
        for var in missing_vars:
            print(f"   - {var}")
    
    if invalid_vars:
        print("⚠️  环境变量配置有问题:")
        for var in invalid_vars:
            print(f"   - {var}")
    
    if not missing_vars and not invalid_vars:
        print("✅ 所有必需的环境变量都已正确配置")
        return True
    
    return False

def check_optional_vars():
    """检查可选的环境变量"""
    optional_vars = {
        "REQUEST_TIMEOUT": ("60.0", "API请求超时时间"),
        "MAX_RETRIES": ("3", "API请求最大重试次数"),
        "ALLOWED_ORIGINS": ("*", "允许的前端域名"),
        "LOG_LEVEL": ("INFO", "日志级别"),
        "MAX_FILE_SIZE": ("10", "最大文件大小(MB)"),
        "ALLOWED_EXTENSIONS": (".txt,.csv", "支持的文件扩展名")
    }
    
    print("\n📋 可选环境变量配置:")
    for var, (default, description) in optional_vars.items():
        value = os.getenv(var, default)
        print(f"   {var}: {value} ({description})")

def test_api_connection():
    """测试API连接（简单验证）"""
    try:
        from autogen_ext.models.openai import OpenAIChatCompletionClient
        from autogen_core.models import ModelFamily
        
        model = os.getenv("MODEL", "deepseek-chat")
        api_key = os.getenv("API_KEY")
        base_url = os.getenv("BASE_URL", "https://api.deepseek.com/v1")
        
        if not api_key or api_key == "your_api_key_here":
            print("⚠️  无法测试API连接 - API密钥未正确配置")
            return False
        
        # 创建客户端（不实际调用API）
        client = OpenAIChatCompletionClient(
            model=model,
            api_key=api_key,
            base_url=base_url,
            timeout=5.0,
            max_retries=1,
            model_info={
                "vision": False,
                "function_calling": True,
                "json_output": True,
                "family": ModelFamily.UNKNOWN,
                "structured_output": True,
                "multiple_system_messages": True,
            }
        )
        
        print("✅ API客户端创建成功")
        print("💡 实际API连接需要在运行时测试")
        return True
        
    except ImportError as e:
        print(f"❌ 缺少必需的依赖包: {e}")
        print("💡 请运行: pip install -r requirements.txt")
        return False
    except Exception as e:
        print(f"❌ API客户端创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 FMEA信号接口智能映射工具 - 配置检查")
    print("=" * 50)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查步骤
    checks = [
        ("检查.env文件", check_env_file),
        ("检查必需环境变量", check_required_vars),
        ("检查API客户端", test_api_connection),
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"\n🔍 {name}...")
        if not check_func():
            all_passed = False
    
    # 显示可选变量
    check_optional_vars()
    
    # 总结
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 配置检查通过！可以启动服务了")
        print("💡 运行命令: python main.py")
    else:
        print("❌ 配置检查失败，请修复上述问题后重试")
        print("💡 参考文档: README.md")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
