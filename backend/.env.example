# FMEA信号接口智能映射工具 - 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# ===========================================
# LLM模型配置
# ===========================================

# 模型名称 (例如: deepseek-chat, gpt-3.5-turbo, gpt-4, etc.)
MODEL=deepseek-chat

# API密钥 (请替换为您的实际API密钥)
API_KEY=your_api_key_here

# API基础URL (根据您使用的服务提供商调整)
BASE_URL=https://api.deepseek.com/v1

# ===========================================
# 服务器配置
# ===========================================

# 后端服务器主机地址
HOST=0.0.0.0

# 后端服务器端口
PORT=8000

# ===========================================
# 请求配置
# ===========================================

# API请求超时时间 (秒)
REQUEST_TIMEOUT=60.0

# API请求最大重试次数
MAX_RETRIES=3

# ===========================================
# 跨域配置
# ===========================================

# 允许的前端域名 (生产环境中应设置具体域名，开发环境可使用 *)
ALLOWED_ORIGINS=*

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# ===========================================
# 文件处理配置
# ===========================================

# 最大文件大小 (MB)
MAX_FILE_SIZE=10

# 支持的文件扩展名 (逗号分隔)
ALLOWED_EXTENSIONS=.txt,.csv,.xlsx,.xls

# ===========================================
# 使用说明
# ===========================================
# 1. 复制此文件为 .env: cp .env.example .env
# 2. 编辑 .env 文件，填入您的实际配置值
# 3. 确保 .env 文件不要提交到版本控制系统中
# 4. 重启后端服务以使配置生效
